import openai
import pandas as pd
import json
import re
import os
import yaml
from pathlib import Path
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# --- Setup logging for OWNERSHIP_PERCENTAGE check ---
log_path = Path("data/audit_logs/data_validation.log")
log_path.parent.mkdir(parents=True, exist_ok=True)
logging.basicConfig(filename=log_path, level=logging.INFO, format='%(asctime)s - %(message)s')

# --- Load configuration files ---
def load_prompt_template(prompt_path):
    with open(prompt_path, "r") as file:
        return file.read()

def load_validation_rules(rules_path):
    with open(rules_path, "r") as file:
        return json.load(file)

# --- Load model configuration ---
def load_model_config():
    try:
        with open("config/model_config.yaml", "r") as file:
            return yaml.safe_load(file)


# --- Validate row using OpenAI ---
def validate_row(prompt_template, validation_rules, row, row_index, client, model_config):
    # Construct the prompt for the current row
    filled_prompt = prompt_template.format(
        validation_rules=json.dumps(validation_rules, indent=2),
        row_index=row_index,
        row_data=row.to_dict()
    )

    # Call OpenAI LLM using the modern client
    try:
        response = client.chat.completions.create(
            model=model_config.get("openai", {}).get("model", "gpt-4-1106-preview"),
            messages=[
                {"role": "user", "content": filled_prompt}
            ],
            temperature=model_config.get("openai", {}).get("temperature", 0.2)
        )
        content = response.choices[0].message.content.strip()
        return json.loads(content)
    except Exception as e:
        logging.error(f"Row {row_index}: Validation error - {str(e)}")
        return {"is_correct": False, "why": f"Error during validation: {str(e)}"}

# --- Check ownership percentage total ---
def check_ownership_percentage_sum(df):
    if "OWNERSHIP_PERCENTAGE" in df.columns:
        try:
            total = df["OWNERSHIP_PERCENTAGE"].astype(float).sum()
            if abs(total - 100) > 0.01:  # Allowing small floating point error
                logging.info("OWNERSHIP_PERCENTAGE total is not 100. Found: %.2f", total)
        except Exception as e:
            logging.info("OWNERSHIP_PERCENTAGE validation error: %s", str(e))

# --- Main function ---
def main():
    prompt_path = "ContextGuardrail/data_validation_prompts.txt"
    rules_path = "data/outputs/data_validation.json"
    file_path = input("📥 Enter path to the Excel file to validate: ").strip()

    # Load configurations
    prompt_template = load_prompt_template(prompt_path)
    validation_rules = load_validation_rules(rules_path)
    model_config = load_model_config()

    # Initialize OpenAI client
    from openai import OpenAI
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    # Load Excel file
    df = pd.read_excel(file_path)

    # Check ownership percentage across the dataset
    check_ownership_percentage_sum(df)

    # Initialize a column for reasons
    df['reason'] = ""

    # Track UNIQUE_IDs to check duplicates
    seen_ids = set()
    duplicate_ids = set()

    if "UNIQUE_ID" in df.columns:
        for uid in df["UNIQUE_ID"]:
            if pd.isna(uid) or uid == "":
                continue
            if uid in seen_ids:
                duplicate_ids.add(uid)
            seen_ids.add(uid)

    # Validate each row
    for index, row in df.iterrows():
        validation_result = validate_row(prompt_template, validation_rules, row, index + 1, client, model_config)
        if not validation_result["is_correct"]:
            df.at[index, 'reason'] = validation_result["why"]

        # Handle duplicate ID flagging manually
        uid = row.get("UNIQUE_ID")
        if uid in duplicate_ids:
            df.at[index, 'reason'] += " Duplicate UNIQUE_ID."

    # Save results to data/outputs directory
    outputs_dir = Path("data/outputs")
    outputs_dir.mkdir(parents=True, exist_ok=True)

    # Create output file paths
    output_file_excel = outputs_dir / (Path(file_path).stem + "_validated.xlsx")
    output_file_json = outputs_dir / (Path(file_path).stem + "_validated.json")

    # Save Excel file
    df.to_excel(output_file_excel, index=False)

    # Save JSON file
    df.to_json(output_file_json, orient='records', indent=2)

    print(f"\n✅ Validation completed. Output saved to: {output_file_excel}")
    print(f"📄 JSON output saved to: {output_file_json}")


if __name__ == "__main__":
    main()
