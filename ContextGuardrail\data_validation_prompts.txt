You are a data validation expert. Your task is to validate data row by row. Using the following rules and basic checks.

SPECIFIC VALIDATION RULES:
{validation_rules}

1. Primary Validation rules from data_validation.json:-
- Use data_validation.json present in "data\outputs\data_validation.json" for validating the data of the files provoded by the User.
- If there is any error/ ambiguty then mention then into the column reason field in front of each row.


2. BASIC VALIDATIONS TO ALWAYS APPLY:
- Determine the data type of each column (integer, float, string, date, datetime, boolean). Determined by LLM.
- Numeric fields should contain valid numbers
- Identify the Unique ID and it should not be duplicate, null, invalid characters, etc.
- Percentage values must be between 0 and 100
- Required fields must not be null, empty, or whitespace
- IDs should follow expected formats
- Dates must be in standard format (e.g., YYYY-MM-DD)
- Text fields must not have invalid characters
- Business logic (e.g., financial fields must be reasonable, no negative values if not expected)

3. OWNERSHIP_PERCENTAGE rule:-
- If the OWNERSHIP_PERCENTAGE column sum is equal to 100, then the data is valid. Otherwise, it is invalid.
- If the OWNERSHIP_PERCENTAGE column sum is not equal to 100 then show error message in log "data\audit_logs\data_validation.log".



ROW DATA (Row #{row_index}):
{row_data}

INSTRUCTIONS:
1. Validate the row against all applicable rules
2. Return valid JSON only — do NOT include markdown, explanations, or extra text
3. Response format:
{{"is_correct": true/false, "why": "reason for validation result"}}
