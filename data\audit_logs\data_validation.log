2025-07-14 16:34:44,706 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 16:34:44,707 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 16:34:44,707 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 16:34:44,904 - INFO - Number of rows to validate: 100
2025-07-14 16:34:44,905 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 16:37:59,096 - INFO - Validation completed. Output saved to: data\outputs\FundHoldings_WithErrors_validated.xlsx
2025-07-14 16:47:33,086 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 16:47:33,086 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 16:47:33,088 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 16:47:33,291 - INFO - Number of rows to validate: 100
2025-07-14 16:47:33,291 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 16:47:33,293 - INFO - Row 4: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
2025-07-14 16:47:33,319 - INFO - Row 4: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
2025-07-14 16:47:35,059 - INFO - Row 11: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-14 16:47:35,059 - INFO - Row 8: OK - All data fields comply with the validation rules.
2025-07-14 16:47:35,059 - INFO - Row 12: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-14 16:47:35,059 - INFO - Row 11: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-14 16:47:35,059 - INFO - Row 12: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-14 16:47:35,084 - INFO - Row 10: OK - All validation rules are satisfied for Row #10
2025-07-14 16:47:35,088 - INFO - Row 2: OK - All validation rules have been met for the given row.
2025-07-14 16:47:35,121 - INFO - Row 3: OK - All validation rules have been successfully applied and the row data has passed all checks.
2025-07-14 16:47:35,277 - INFO - Row 6: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:47:35,330 - INFO - Row 18: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
2025-07-14 16:47:35,334 - INFO - Row 5: FAIL - UNIQUE_ID contains an underscore which is not an alphanumeric character.
2025-07-14 16:47:35,335 - INFO - Row 18: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
2025-07-14 16:47:35,348 - INFO - Row 7: OK - All validation checks passed
2025-07-14 16:47:35,524 - INFO - Row 9: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value
2025-07-14 16:47:36,395 - INFO - Row 15: OK - All validation rules passed for row #15
2025-07-14 16:47:36,528 - INFO - Row 19: OK - All validation rules have been met for the row data.
2025-07-14 16:47:36,742 - INFO - Row 13: OK - All validation rules have been applied and the data in the row is correct.
2025-07-14 16:47:36,784 - INFO - Row 20: OK - All validation rules have been passed for Row #20.
2025-07-14 16:47:36,821 - INFO - Row 26: FAIL - Pydantic validation error: 3 validation errors for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
NO_OF_SHARES
  Input should be a finite number [type=finite_number, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/finite_number
2025-07-14 16:47:36,823 - INFO - Row 16: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:47:36,825 - INFO - Row 26: FAIL - Pydantic validation error: 3 validation errors for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
NO_OF_SHARES
  Input should be a finite number [type=finite_number, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/finite_number
2025-07-14 16:47:36,887 - INFO - Row 17: OK - All validation rules have been successfully applied and the data row is correct.
2025-07-14 16:47:37,008 - INFO - Row 21: OK - All validation rules have been passed for Row #21
2025-07-14 16:47:37,771 - INFO - Row 22: OK - All validation rules have been passed for Row #22.
2025-07-14 16:47:38,050 - INFO - Row 24: OK - All validation rules have been passed for Row #24
2025-07-14 16:47:38,062 - INFO - Row 23: OK - All validation rules have been met for row #23.
2025-07-14 16:47:38,160 - INFO - Row 28: OK - All validation rules have been passed for row #28
2025-07-14 16:47:38,261 - INFO - Row 34: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
2025-07-14 16:47:38,261 - INFO - Row 29: OK - All validation rules have been passed for row #29
2025-07-14 16:47:38,263 - INFO - Row 34: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
2025-07-14 16:47:38,518 - INFO - Row 27: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value.
2025-07-14 16:47:38,840 - INFO - Row 14: OK - All validation rules have been met for Row #14. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. All fields contain valid data types and formats as per the given rules.
2025-07-14 16:47:39,541 - INFO - Row 30: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100
2025-07-14 16:47:39,683 - INFO - Row 33: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 16:47:39,799 - INFO - Row 31: OK - All validation rules have been passed for Row #31
2025-07-14 16:47:39,816 - INFO - Row 32: OK - All validation rules have been passed for the given row data.
2025-07-14 16:47:39,943 - INFO - Row 35: OK - All validation rules have been applied and the data in the row is correct.
2025-07-14 16:47:40,114 - INFO - Row 25: OK - All validation rules passed. No null or zero values in required fields, UNIQUE_ID does not start with 'TOTAL' and contains only alphanumeric characters, no duplicates mentioned, numeric fields contain valid numbers, percentage is between 0 and 100, required fields are not null, empty, or whitespace, and business logic is maintained.
2025-07-14 16:47:40,151 - INFO - Row 36: OK - All validation rules and basic checks are passed
2025-07-14 16:47:40,353 - INFO - Row 45: FAIL - Pydantic validation error: 3 validation errors for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
NO_OF_SHARES
  Input should be a finite number [type=finite_number, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/finite_number
2025-07-14 16:47:40,356 - INFO - Row 37: OK - All data in the row passes the specified validation rules.
2025-07-14 16:47:40,356 - INFO - Row 45: FAIL - Pydantic validation error: 3 validation errors for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
NO_OF_SHARES
  Input should be a finite number [type=finite_number, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/finite_number
2025-07-14 16:47:41,121 - INFO - Row 39: OK - All validation rules have been applied successfully and no issues were found.
2025-07-14 16:47:41,258 - INFO - Row 40: OK - All data fields pass the specific validation rules and basic checks.
2025-07-14 16:47:41,341 - INFO - Row 49: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
2025-07-14 16:47:41,355 - INFO - Row 42: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not a valid percentage value.
2025-07-14 16:47:41,355 - INFO - Row 49: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
2025-07-14 16:47:41,379 - INFO - Row 41: OK - All validation rules have been applied successfully and no issues were found.
2025-07-14 16:47:41,458 - INFO - Row 43: FAIL - NAV is null which violates the null_check rule
2025-07-14 16:47:41,802 - INFO - Row 53: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
2025-07-14 16:47:41,802 - INFO - Row 54: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-14 16:47:41,802 - INFO - Row 44: FAIL - NAV is null which violates the null_check rule
2025-07-14 16:47:41,802 - INFO - Row 53: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
2025-07-14 16:47:41,802 - INFO - Row 54: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-14 16:47:41,920 - INFO - Row 46: OK - All validation rules have been met for Row #46.
2025-07-14 16:47:42,438 - INFO - Row 47: OK - All validation rules passed
2025-07-14 16:47:42,675 - INFO - Row 48: OK - All data fields are valid according to the specified validation rules.
2025-07-14 16:47:43,007 - INFO - Row 38: OK - All validation rules have been met for Row #38. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. Numeric fields contain valid numbers, and the PERIOD is in a standard format.
2025-07-14 16:47:43,229 - INFO - Row 51: OK - All validation rules have been successfully passed for Row #51.
2025-07-14 16:47:43,276 - INFO - Row 50: OK - All validation rules have been applied and the data in the row is correct.
2025-07-14 16:47:43,428 - INFO - Row 55: OK - All validation rules have been met for row #55
2025-07-14 16:47:43,626 - INFO - Row 56: OK - All validation rules have been applied and the row data passes all checks.
2025-07-14 16:47:43,860 - INFO - Row 52: FAIL - NAV is null which violates the null_check rule
2025-07-14 16:47:43,904 - INFO - Row 57: OK - All validation rules have been passed for row #57
2025-07-14 16:47:44,179 - INFO - Row 58: OK - All validation rules have been met for row #58.
2025-07-14 16:47:44,620 - INFO - Row 59: OK - All data fields comply with the specified validation rules.
2025-07-14 16:47:44,938 - INFO - Row 63: OK - All data fields are valid according to the given validation rules.
2025-07-14 16:47:45,174 - INFO - Row 65: OK - All validation rules have been passed for Row #65
2025-07-14 16:47:45,351 - INFO - Row 61: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:47:45,352 - INFO - Row 64: OK - All validation rules have been passed for Row #64
2025-07-14 16:47:45,643 - INFO - Row 66: OK - All validation rules have been successfully passed for Row #66.
2025-07-14 16:47:45,938 - INFO - Row 67: OK - All data in the row passes the specified validation rules.
2025-07-14 16:47:46,587 - INFO - Row 69: OK - All validation rules have been passed for Row #69
2025-07-14 16:47:46,753 - INFO - Row 75: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-14 16:47:46,753 - INFO - Row 68: OK - All validation rules have been passed for Row #68.
2025-07-14 16:47:46,753 - INFO - Row 71: OK - All validation rules have been met for the given row data.
2025-07-14 16:47:46,753 - INFO - Row 75: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-14 16:47:46,920 - INFO - Row 70: OK - All validation checks passed
2025-07-14 16:47:46,920 - INFO - Row 60: OK - All validation rules have been met for Row #60. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. Numeric fields contain valid numbers, and all required fields are not null, empty, or whitespace.
2025-07-14 16:47:47,169 - INFO - Row 72: OK - All validation rules have been passed for row #72.
2025-07-14 16:47:47,244 - INFO - Row 73: OK - All validation rules have been passed for row #73.
2025-07-14 16:47:47,920 - INFO - Row 62: FAIL - NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero; UNIQUE_ID should not start with 'TOTAL' (case insensitive), should only contain alphanumeric characters, and should not be null, empty, or have duplicates; Numeric fields should contain valid numbers; Percentage values must be between 0 and 100; Required fields must not be null, empty, or whitespace; Dates must be in standard format (e.g., YYYY-MM-DD); Business logic must be reasonable (e.g., no negative values if not expected). The OWNERSHIP_PERCENTAGE is above 100.
2025-07-14 16:47:47,985 - INFO - Row 74: OK - All data validation rules have been met for Row #74.
2025-07-14 16:47:48,057 - INFO - Row 77: FAIL - OWNERSHIP_PERCENTAGE must be between 0 and 100.
2025-07-14 16:47:48,326 - INFO - Row 76: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 16:47:48,570 - INFO - Row 79: OK - All data points are valid according to the specified validation rules.
2025-07-14 16:47:48,847 - INFO - Row 80: OK - All data validation rules are satisfied for Row #80.
2025-07-14 16:47:49,002 - INFO - Row 81: OK - All validation rules have been passed for Row #81.
2025-07-14 16:47:49,030 - INFO - Row 78: FAIL - COMMITTED_CAPITAL should not be null or zero but the value is too low compared to other financial fields, which might indicate a data entry error.
2025-07-14 16:47:49,171 - INFO - Row 90: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-14 16:47:49,171 - INFO - Row 83: FAIL - NAV should not be null or zero
2025-07-14 16:47:49,182 - INFO - Row 82: OK - All validation rules have been passed for Row #82
2025-07-14 16:47:49,182 - INFO - Row 90: FAIL - Pydantic validation error: 1 validation error for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-14 16:47:49,632 - INFO - Row 85: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:47:49,959 - INFO - Row 84: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value
2025-07-14 16:47:49,965 - INFO - Row 86: OK - All validation rules have been applied and the data in Row #86 meets the specified requirements.
2025-07-14 16:47:50,353 - INFO - Row 89: OK - All validation rules have been successfully applied and the data row is correct.
2025-07-14 16:47:50,448 - INFO - Row 87: OK - All data fields are valid as per the specific validation rules and basic checks.
2025-07-14 16:47:50,550 - INFO - Row 98: FAIL - Pydantic validation error: 3 validation errors for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
NO_OF_SHARES
  Input should be a finite number [type=finite_number, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/finite_number
2025-07-14 16:47:50,551 - INFO - Row 88: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:47:50,552 - INFO - Row 98: FAIL - Pydantic validation error: 3 validation errors for FundHoldingRow
UNIQUE_ID
  Input should be a valid string [type=string_type, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
OWNERSHIP_PERCENTAGE
  Input should be less than or equal to 100 [type=less_than_equal, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/less_than_equal
NO_OF_SHARES
  Input should be a finite number [type=finite_number, input_value=nan, input_type=float]
    For further information visit https://errors.pydantic.dev/2.5/v/finite_number
2025-07-14 16:47:50,623 - INFO - Row 92: FAIL - NAV is null which violates the null_check rule
2025-07-14 16:47:51,039 - INFO - Row 91: FAIL - OWNERSHIP_PERCENTAGE is not between 0 and 100
2025-07-14 16:47:51,244 - INFO - Row 94: OK - All data fields conform to the specified validation rules.
2025-07-14 16:47:51,358 - INFO - Row 93: OK - All validation rules have been met for Row #93.
2025-07-14 16:47:51,489 - INFO - Row 95: OK - All validation rules have been passed for Row #95.
2025-07-14 16:47:51,745 - INFO - Row 96: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 16:47:51,775 - INFO - Row 99: OK - All validation rules are satisfied for the given row data.
2025-07-14 16:47:52,108 - INFO - Row 97: OK - All validation rules have been passed for row #97
2025-07-14 16:47:52,218 - INFO - Row 101: OK - All validation checks passed
2025-07-14 16:47:52,371 - INFO - Row 100: FAIL - OWNERSHIP_PERCENTAGE must be between 0 and 100
2025-07-14 16:47:52,424 - INFO - Validation completed. Output saved to: data\outputs\FundHoldings_WithErrors_validated.xlsx
2025-07-14 16:50:35,809 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 16:50:35,810 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 16:50:35,810 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 16:50:36,018 - INFO - Number of rows to validate: 100
2025-07-14 16:50:36,018 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 16:50:37,720 - INFO - Row 8: OK - All data fields comply with the validation rules.
2025-07-14 16:50:37,738 - INFO - Row 4: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero
2025-07-14 16:50:37,771 - INFO - Row 5: OK - All validation rules have been met for the row data.
2025-07-14 16:50:37,825 - INFO - Row 2: OK - All validation rules have been met for the provided row data.
2025-07-14 16:50:37,935 - INFO - Row 7: OK - All data fields adhere to the specified validation rules.
2025-07-14 16:50:38,075 - INFO - Row 6: OK - All validation checks passed
2025-07-14 16:50:38,320 - INFO - Row 3: OK - All validation rules have been met for Row #3.
2025-07-14 16:50:38,532 - INFO - Row 9: FAIL - OWNERSHIP_PERCENTAGE is out of the expected range of 0 to 100
2025-07-14 16:50:39,438 - INFO - Row 10: OK - All validation rules have been met for Row #10
2025-07-14 16:50:39,452 - INFO - Row 13: OK - All validation rules have been passed for row #13.
2025-07-14 16:50:39,665 - INFO - Row 14: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100.
2025-07-14 16:50:39,792 - INFO - Row 16: OK - All validation rules have been passed for the row data.
2025-07-14 16:50:39,926 - INFO - Row 15: OK - All data validation rules have been successfully applied and the row data passes all checks.
2025-07-14 16:50:40,031 - INFO - Row 11: FAIL - UNIQUE_ID is null or empty, which violates the null_check rule; OWNERSHIP_PERCENTAGE must be between 0 and 100, which violates the percentage value rule
2025-07-14 16:50:40,072 - INFO - Row 17: OK - All validation rules have been successfully applied and the row data has passed all checks.
2025-07-14 16:50:40,097 - INFO - Row 12: FAIL - UNIQUE_ID is null or empty which violates the null_check rule, and NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero, but UNIQUE_ID is null.
2025-07-14 16:50:40,851 - INFO - Row 19: OK - All data in the row meets the validation criteria.
2025-07-14 16:50:40,867 - INFO - Row 20: OK - All validation rules have been met for Row #20.
2025-07-14 16:50:40,974 - INFO - Row 18: FAIL - OWNERSHIP_PERCENTAGE is not a valid number; it should not be null or NaN.
2025-07-14 16:50:41,202 - INFO - Row 21: OK - All validation rules have been passed for Row #21.
2025-07-14 16:50:41,253 - INFO - Row 23: OK - All validation rules have been met for row #23.
2025-07-14 16:50:41,433 - INFO - Row 25: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not a valid percentage value.
2025-07-14 16:50:41,592 - INFO - Row 24: OK - All validation rules have been passed for row #24
2025-07-14 16:50:41,621 - INFO - Row 22: OK - All validation rules have been passed for Row #22
2025-07-14 16:50:42,667 - INFO - Row 28: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:50:42,704 - INFO - Row 27: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100
2025-07-14 16:50:42,849 - INFO - Row 31: OK - All validation rules have been passed for the row data.
2025-07-14 16:50:42,861 - INFO - Row 30: OK - All validation rules have been passed for Row #30
2025-07-14 16:50:42,908 - INFO - Row 29: OK - All data validation rules have been successfully applied and the row data passes all checks.
2025-07-14 16:50:43,069 - INFO - Row 32: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value
2025-07-14 16:50:43,246 - INFO - Row 26: FAIL - UNIQUE_ID is null or empty, NAV is null or zero, OWNERSHIP_PERCENTAGE is null or zero, CAPITAL_CALLED is null or zero, NO_OF_SHARES is null or zero, COMMITTED_CAPITAL is null or zero, entire row contains null values indicating a potentially empty file or row
2025-07-14 16:50:43,287 - INFO - Row 33: OK - All validation rules have been passed for Row #33
2025-07-14 16:50:44,108 - INFO - Row 37: OK - All validation rules have been passed for row #37.
2025-07-14 16:50:44,108 - INFO - Row 36: OK - All validation rules have been passed for the given row data.
2025-07-14 16:50:44,231 - INFO - Row 38: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:50:44,338 - INFO - Row 39: OK - All validation rules have been applied successfully and no issues were found.
2025-07-14 16:50:44,433 - INFO - Row 34: FAIL - OWNERSHIP_PERCENTAGE is not a valid number between 0 and 100
2025-07-14 16:50:44,671 - INFO - Row 41: OK - All validation rules have been applied and the data is correct.
2025-07-14 16:50:44,839 - INFO - Row 40: OK - All validation rules have been successfully applied and the data row is correct.
2025-07-14 16:50:45,271 - INFO - Row 43: FAIL - NAV is null which violates the null_check rule
2025-07-14 16:50:45,429 - INFO - Row 44: FAIL - NAV should not be null or zero
2025-07-14 16:50:45,874 - INFO - Row 42: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value.
2025-07-14 16:50:46,130 - INFO - Row 46: OK - All validation rules have been passed for row #46
2025-07-14 16:50:46,191 - INFO - Row 35: OK - All validation rules passed. The UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within 0 and 100. Numeric fields contain valid numbers. No duplicates of UNIQUE_ID are mentioned, and all required fields are present and correctly formatted.
2025-07-14 16:50:46,293 - INFO - Row 48: OK - All data fields are valid according to the specified validation rules.
2025-07-14 16:50:46,794 - INFO - Row 50: OK - All validation rules have been passed for Row #50.
2025-07-14 16:50:47,291 - INFO - Row 49: FAIL - OWNERSHIP_PERCENTAGE is not a valid number between 0 and 100; UNIQUE_ID should not be null or empty; OWNERSHIP_PERCENTAGE should not be null or zero
2025-07-14 16:50:47,633 - INFO - Row 47: OK - All validation rules passed. No null or zero values in specified fields, UNIQUE_ID does not start with 'TOTAL' and contains only alphanumeric characters, UNIQUE_ID is not null or empty and no duplicates mentioned, numeric fields contain valid numbers, percentage value is within 0 and 100, required fields are not null, empty, or whitespace, and all fields follow expected formats and business logic.
2025-07-14 16:50:47,788 - INFO - Row 45: FAIL - UNIQUE_ID is null or empty, NAV is null or zero, OWNERSHIP_PERCENTAGE is null or zero, CAPITAL_CALLED is null or zero, NO_OF_SHARES is null or zero, COMMITTED_CAPITAL is null or zero, file should not be empty, UNIQUE_ID should not be null or empty, and UNIQUE_ID should not have duplicates
2025-07-14 16:50:47,849 - INFO - Row 53: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero, and percentage values must be between 0 and 100
2025-07-14 16:50:48,231 - INFO - Row 51: OK - All validation rules have been passed
2025-07-14 16:50:48,532 - INFO - Row 55: OK - All validation rules have been passed for row #55
2025-07-14 16:50:48,889 - INFO - Row 57: OK - All validation rules have been passed for Row #57
2025-07-14 16:50:49,049 - INFO - Row 58: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:50:49,327 - INFO - Row 52: FAIL - NAV is null; it should not be null or zero as per null_check rule.
2025-07-14 16:50:49,552 - INFO - Row 59: OK - All validation rules passed
2025-07-14 16:50:49,973 - INFO - Row 60: OK - All validation rules have been met for the row data.
2025-07-14 16:50:50,036 - INFO - Row 61: OK - All validation rules have been passed for Row #61
2025-07-14 16:50:50,506 - INFO - Row 63: OK - All data in the row meets the validation criteria.
2025-07-14 16:50:50,711 - INFO - Row 64: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 16:50:50,723 - INFO - Row 56: OK - All validation rules have been met for Row #56. The UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. The OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. All fields contain valid data types and there are no duplicates of UNIQUE_ID reported.
2025-07-14 16:50:51,308 - INFO - Row 65: OK - All validation rules have been passed for Row #65.
2025-07-14 16:50:51,439 - INFO - Row 66: OK - All validation rules have been successfully applied and the row data passes all checks.
2025-07-14 16:50:51,593 - INFO - Row 54: FAIL - UNIQUE_ID is null or empty which violates the null_check rule; NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero, but UNIQUE_ID is null; UNIQUE_ID should not start with 'TOTAL' (case insensitive), but since it is null, this check cannot be performed; UNIQUE_ID should only contain alphanumeric characters, but since it is null, this check cannot be performed; UNIQUE_ID should not have duplicates, but since it is null, this check cannot be performed.
2025-07-14 16:50:52,053 - INFO - Row 69: OK - All validation rules have been passed for Row #69.
2025-07-14 16:50:52,095 - INFO - Row 68: OK - All validation rules have been passed for Row #68
2025-07-14 16:50:52,104 - INFO - Row 62: FAIL - NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero, UNIQUE_ID should not start with 'TOTAL' (case insensitive), should only contain alphanumeric characters, and should not be null, empty, or have duplicates. The UNIQUE_ID 'UID_8408' is valid, but the OWNERSHIP_PERCENTAGE '60.34' exceeds the allowed maximum of 100 for percentage values.
2025-07-14 16:50:52,450 - INFO - Row 70: FAIL - UNIQUE_ID contains an underscore which is not an alphanumeric character.
2025-07-14 16:50:52,786 - INFO - Row 72: OK - All validation rules have been passed
2025-07-14 16:50:53,168 - INFO - Row 73: OK - All validation rules have been passed for Row #73
2025-07-14 16:50:53,400 - INFO - Row 74: OK - All validation rules have been passed for Row #74
2025-07-14 16:50:53,852 - INFO - Row 75: FAIL - UNIQUE_ID is null or empty which violates the null_check rule; UNIQUE_ID should not be null or empty.
2025-07-14 16:50:54,017 - INFO - Row 77: OK - All validation rules have been met for Row #77
2025-07-14 16:50:54,401 - INFO - Row 71: OK - All validation rules have been met for Row #71. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. All fields meet the basic validation requirements.
2025-07-14 16:50:54,592 - INFO - Row 79: OK - All specified validation rules and basic checks are passed
2025-07-14 16:50:54,618 - INFO - Row 80: OK - All validation rules have been met for the row data.
2025-07-14 16:50:55,035 - INFO - Row 78: FAIL - COMMITTED_CAPITAL should not be null or zero but the provided value is significantly lower than expected for a financial field, suggesting a possible data entry error.
2025-07-14 16:50:55,621 - INFO - Row 76: OK - All validation rules have been met for Row #76. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, is not null or empty, and assuming no duplicates exist in the dataset. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. All fields contain valid data types and values according to the specified rules.
2025-07-14 16:50:55,678 - INFO - Row 81: OK - All validation rules have been passed for Row #81.
2025-07-14 16:50:55,928 - INFO - Row 85: OK - All data fields comply with the specified validation rules and basic checks.
2025-07-14 16:50:55,945 - INFO - Row 83: FAIL - NAV is null which violates the null_check rule
2025-07-14 16:50:55,971 - INFO - Row 82: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value.
2025-07-14 16:50:56,118 - INFO - Row 84: OK - All validation rules have been successfully passed for Row #84
2025-07-14 16:50:56,522 - INFO - Row 86: OK - All validation rules have been passed for Row #86.
2025-07-14 16:50:56,920 - INFO - Row 88: OK - All validation rules have been met for Row #88.
2025-07-14 16:50:57,246 - INFO - Row 87: OK - All validation rules have been successfully applied and the row data meets all the specified requirements.
2025-07-14 16:50:57,319 - INFO - Row 91: FAIL - OWNERSHIP_PERCENTAGE is not between 0 and 100
2025-07-14 16:50:57,632 - INFO - Row 92: FAIL - NAV is null; it should not be null or zero as per null_check rule
2025-07-14 16:50:57,760 - INFO - Row 89: OK - All validation rules have been successfully passed for Row #89.
2025-07-14 16:50:57,792 - INFO - Row 90: FAIL - UNIQUE_ID is null or empty which violates the null_check rule. OWNERSHIP_PERCENTAGE is greater than 100 which violates the percentage value rule.
2025-07-14 16:50:58,489 - INFO - Row 94: OK - All validation rules have been applied successfully and no errors were found.
2025-07-14 16:50:58,862 - INFO - Row 95: FAIL - The NO_OF_SHARES field contains a decimal value, which is not valid for a count of shares. This field should contain an integer.
2025-07-14 16:50:58,877 - INFO - Row 96: OK - All validation rules have been passed
2025-07-14 16:50:59,394 - INFO - Row 99: OK - All validation rules have been successfully applied and the data row is correct.
2025-07-14 16:50:59,525 - INFO - Row 97: OK - All validation rules have been passed for row #97.
2025-07-14 16:50:59,903 - INFO - Row 100: FAIL - OWNERSHIP_PERCENTAGE must be between 0 and 100
2025-07-14 16:51:00,730 - INFO - Row 101: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value.
2025-07-14 16:51:00,961 - INFO - Row 98: FAIL - Row #98 has multiple validation issues: UNIQUE_ID is null or empty, which violates the null_check rule; NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL are null or zero, which violates the null_check rule; The entire row is null, indicating the file may be empty, which violates the business_rule.
2025-07-14 16:57:28,208 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 16:57:28,209 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 16:57:28,209 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 16:57:28,372 - INFO - Number of rows to validate: 100
2025-07-14 16:57:28,372 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 16:57:30,002 - INFO - Row 8: OK - All data fields adhere to the specified validation rules.
2025-07-14 16:57:30,081 - INFO - Row 3: OK - All validation rules have been applied and the data in the row is correct.
2025-07-14 16:57:30,101 - INFO - Row 6: OK - All data fields comply with the specified validation rules.
2025-07-14 16:57:30,149 - INFO - Row 9: FAIL - OWNERSHIP_PERCENTAGE must be between 0 and 100
2025-07-14 16:57:30,171 - INFO - Row 5: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 16:57:30,174 - INFO - Row 7: OK - All data fields pass the specific validation rules and basic checks.
2025-07-14 16:57:30,242 - INFO - Row 2: OK - All validation rules have been passed for the given row data.
2025-07-14 16:57:30,958 - INFO - Row 4: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero; OWNERSHIP_PERCENTAGE must be a valid number between 0 and 100
2025-07-14 16:57:31,491 - INFO - Row 15: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:57:31,680 - INFO - Row 14: OK - All validation rules have been passed for the row data.
2025-07-14 16:57:31,685 - INFO - Row 13: OK - All validation rules passed for Row #13
2025-07-14 16:57:31,773 - INFO - Row 10: OK - All data in the row meets the specified validation rules.
2025-07-14 16:57:31,893 - INFO - Row 16: FAIL - OWNERSHIP_PERCENTAGE must be between 0 and 100.
2025-07-14 16:57:32,159 - INFO - Row 12: FAIL - UNIQUE_ID is null or empty, which violates the null_check rule. OWNERSHIP_PERCENTAGE exceeds 100, which violates the percentage value rule.
2025-07-14 16:57:32,213 - INFO - Row 17: OK - All validation rules have been successfully applied and the data row is correct.
2025-07-14 16:57:32,226 - INFO - Row 11: FAIL - UNIQUE_ID is null which violates the null_check rule, and NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero but UNIQUE_ID is null
2025-07-14 16:57:32,829 - INFO - Row 19: OK - All validation rules have been passed for Row #19
2025-07-14 16:57:33,056 - INFO - Row 21: OK - All validation rules have been passed for Row #21.
2025-07-14 16:57:33,107 - INFO - Row 22: OK - All validation checks passed
2025-07-14 16:57:33,348 - INFO - Row 18: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero; it contains 'nan'.
2025-07-14 16:57:33,425 - INFO - Row 24: OK - All data in the row passes the specified validation rules.
2025-07-14 16:57:33,759 - INFO - Row 25: OK - All validation rules have been met for row #25
2025-07-14 16:57:34,330 - INFO - Row 28: OK - All validation rules have been passed for row #28
2025-07-14 16:57:34,590 - INFO - Row 29: OK - All validation rules have been passed for row #29
2025-07-14 16:57:34,664 - INFO - Row 30: OK - All validation rules passed
2025-07-14 16:57:34,782 - INFO - Row 27: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value.
2025-07-14 16:57:35,038 - INFO - Row 31: OK - The row data passes all the specific validation rules and basic checks.
2025-07-14 16:57:35,154 - INFO - Row 26: FAIL - UNIQUE_ID is null or empty, NAV is null or zero, OWNERSHIP_PERCENTAGE is null or zero, CAPITAL_CALLED is null or zero, NO_OF_SHARES is null or zero, COMMITTED_CAPITAL is null or zero, all fields are null which violates the rule that the file should not be empty
2025-07-14 16:57:35,489 - INFO - Row 20: OK - All validation rules have been met for Row #20. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. The data types are appropriate for each field, and there are no indications of duplicates for UNIQUE_ID within this single row context.
2025-07-14 16:57:35,999 - INFO - Row 35: OK - All validation rules have been passed for row #35.
2025-07-14 16:57:36,062 - INFO - Row 32: OK - All validation rules have been successfully applied and the data row is correct.
2025-07-14 16:57:36,117 - INFO - Row 34: FAIL - OWNERSHIP_PERCENTAGE is null and should be between 0 and 100
2025-07-14 16:57:36,218 - INFO - Row 33: OK - All validation rules have been met for Row #33
2025-07-14 16:57:36,667 - INFO - Row 37: OK - All data in the row meets the validation rules
2025-07-14 16:57:36,747 - INFO - Row 38: OK - All validation rules have been applied and the data row is correct.
2025-07-14 16:57:36,820 - INFO - Row 36: OK - All data in the row adheres to the specified validation rules.
2025-07-14 16:57:37,438 - INFO - Row 41: OK - All validation rules have been passed for Row #41.
2025-07-14 16:57:37,495 - INFO - Row 40: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:57:37,538 - INFO - Row 39: OK - All validation rules have been passed for the given row data.
2025-07-14 16:57:37,632 - INFO - Row 23: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:57:37,958 - INFO - Row 42: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage field
2025-07-14 16:57:38,584 - INFO - Row 43: FAIL - NAV should not be null or zero
2025-07-14 16:57:38,584 - INFO - Row 44: FAIL - NAV should not be null or zero
2025-07-14 16:57:38,835 - INFO - Row 47: OK - All validation rules have been met for Row #47.
2025-07-14 16:57:39,361 - INFO - Row 46: OK - All data fields are valid according to the specified validation rules.
2025-07-14 16:57:39,735 - INFO - Row 45: FAIL - UNIQUE_ID is null or empty, NAV is null or zero, OWNERSHIP_PERCENTAGE is null or zero, CAPITAL_CALLED is null or zero, NO_OF_SHARES is null or zero, COMMITTED_CAPITAL is null or zero, all fields are null indicating an empty row which violates the business rule that the file should not be empty
2025-07-14 16:57:39,772 - INFO - Row 51: OK - All validation rules have been successfully passed.
2025-07-14 16:57:39,853 - INFO - Row 49: FAIL - OWNERSHIP_PERCENTAGE is NaN which violates the null_check rule; it should not be null or zero. Additionally, OWNERSHIP_PERCENTAGE should be between 0 and 100 to comply with the percentage value rule.
2025-07-14 16:57:40,039 - INFO - Row 52: FAIL - NAV is null; it should not be null or zero as per null_check rule.
2025-07-14 16:57:41,006 - INFO - Row 57: OK - All validation rules have been passed for Row #57
2025-07-14 16:57:41,159 - INFO - Row 50: OK - All validation rules have been met for Row #50. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. Numeric fields contain valid numbers, and the PERIOD is in a standard format.
2025-07-14 16:57:41,159 - INFO - Row 53: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero; OWNERSHIP_PERCENTAGE must be between 0 and 100
2025-07-14 16:57:41,184 - INFO - Row 48: OK - All validation rules passed. The UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. Numeric fields contain valid numbers. No duplicates for UNIQUE_ID are mentioned, and all required fields are present and not null.
2025-07-14 16:57:41,261 - INFO - Row 55: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:57:41,603 - INFO - Row 58: OK - All validation rules have been met for Row #58.
2025-07-14 16:57:42,449 - INFO - Row 63: OK - All validation rules have been passed.
2025-07-14 16:57:42,541 - INFO - Row 61: OK - All validation rules have been passed for Row #61
2025-07-14 16:57:42,636 - INFO - Row 59: OK - All validation rules are satisfied for the row data
2025-07-14 16:57:42,788 - INFO - Row 60: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 16:57:43,099 - INFO - Row 64: OK - All validation rules have been passed for Row #64.
2025-07-14 16:57:43,194 - INFO - Row 56: OK - All validation rules have been met for Row #56. The UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. There are no null or zero values in NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL. The OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. All required fields are present and contain valid data.
2025-07-14 16:57:43,933 - INFO - Row 65: OK - All data fields are valid as per the given validation rules.
2025-07-14 16:57:44,139 - INFO - Row 66: OK - All validation rules have been passed for Row #66
2025-07-14 16:57:44,473 - INFO - Row 68: OK - All validation rules have been passed for Row #68
2025-07-14 16:57:44,473 - INFO - Row 54: FAIL - UNIQUE_ID is null or empty which violates the null_check rule; NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL are valid; UNIQUE_ID format cannot be validated as it is null; File is not empty; UNIQUE_ID has duplicates cannot be checked as it is null; Data types are correct; Numeric fields contain valid numbers; Percentage value is within valid range; Required fields are not null except for UNIQUE_ID; ID format is not valid due to null value; Dates are in standard format; Text fields do not have invalid characters; Business logic is followed.
2025-07-14 16:57:44,885 - INFO - Row 70: FAIL - OWNERSHIP_PERCENTAGE must be between 0 and 100.
2025-07-14 16:57:45,271 - INFO - Row 67: FAIL - The COMMITTED_CAPITAL field should not be null or zero, but it is significantly lower than expected for the given NAV and CAPITAL_CALLED values, indicating a potential data entry error or inconsistency in business logic.
2025-07-14 16:57:45,479 - INFO - Row 72: OK - All validation checks passed
2025-07-14 16:57:45,627 - INFO - Row 71: OK - All validation rules have been passed for Row #71.
2025-07-14 16:57:45,875 - INFO - Row 69: OK - All data fields are valid according to the provided validation rules.
2025-07-14 16:57:45,875 - INFO - Row 62: FAIL - NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero; UNIQUE_ID should not start with 'TOTAL' (case insensitive), should only contain alphanumeric characters, and should not be null, empty, or have duplicates; Numeric fields should contain valid numbers; Percentage values must be between 0 and 100; Required fields must not be null, empty, or whitespace; Dates must be in standard format (e.g., YYYY-MM-DD). The OWNERSHIP_PERCENTAGE value '60.34' is not between 0 and 100.
2025-07-14 16:57:45,875 - INFO - Row 73: OK - All validation rules have been passed for Row #73
2025-07-14 16:57:46,114 - INFO - Row 74: OK - All validation rules have been passed
2025-07-14 16:57:46,375 - INFO - Row 75: FAIL - UNIQUE_ID is null which violates the null_check rule; UNIQUE_ID should not be null or empty.
2025-07-14 16:57:46,970 - INFO - Row 76: OK - All validation rules have been passed for Row #76
2025-07-14 16:57:47,095 - INFO - Row 77: OK - All validation rules have been met for Row #77.
2025-07-14 16:57:47,102 - INFO - Row 80: OK - All validation rules have been passed for Row #80.
2025-07-14 16:57:47,146 - INFO - Row 79: OK - All validation rules have been successfully applied and the data row is correct.
2025-07-14 16:57:47,506 - INFO - Row 83: FAIL - NAV should not be null or zero
2025-07-14 16:57:47,589 - INFO - Row 82: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value.
2025-07-14 16:57:47,652 - INFO - Row 81: OK - All validation rules have been passed for Row #81.
2025-07-14 16:57:47,799 - INFO - Row 78: FAIL - COMMITTED_CAPITAL should not be null or zero but the provided value is significantly lower than expected for a financial field, indicating a potential data entry error or misclassification.
2025-07-14 16:57:48,504 - INFO - Row 85: OK - All validation rules have been passed for Row #85.
2025-07-14 16:57:48,630 - INFO - Row 88: OK - All validation checks passed
2025-07-14 16:57:48,749 - INFO - Row 84: OK - All data fields comply with the specified validation rules.
2025-07-14 16:57:48,823 - INFO - Row 86: OK - All validation rules are passed for Row #86
2025-07-14 16:57:49,079 - INFO - Row 89: OK - All validation rules have been successfully applied and the row data meets the specified criteria.
2025-07-14 16:57:49,313 - INFO - Row 91: FAIL - OWNERSHIP_PERCENTAGE is not within the valid range of 0 to 100
2025-07-14 16:57:49,753 - INFO - Row 92: FAIL - NAV is null which violates the null_check rule
2025-07-14 16:57:49,844 - INFO - Row 90: FAIL - UNIQUE_ID is null or empty which violates the null_check rule, and NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero but UNIQUE_ID is null.
2025-07-14 16:57:50,085 - INFO - Row 95: OK - All validation rules have been passed for Row #95.
2025-07-14 16:57:50,210 - INFO - Row 87: OK - All validation rules have been met for Row #87. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. All fields meet the basic validation criteria.
2025-07-14 16:57:50,465 - INFO - Row 93: OK - All validation rules have been met for Row #93
2025-07-14 16:57:50,584 - INFO - Row 97: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 16:57:50,734 - INFO - Row 94: FAIL - OWNERSHIP_PERCENTAGE is not between 0 and 100
2025-07-14 16:57:50,942 - INFO - Row 96: OK - All validation rules have been passed for Row #96.
2025-07-14 16:57:51,406 - INFO - Row 99: OK - All validation rules have been met for Row #99.
2025-07-14 16:57:51,456 - INFO - Row 101: OK - All validation rules have been passed for Row #101
2025-07-14 16:57:51,788 - INFO - Row 100: FAIL - OWNERSHIP_PERCENTAGE must be between 0 and 100
2025-07-14 16:57:52,490 - INFO - Row 98: FAIL - UNIQUE_ID is null or empty, NAV is null or zero, OWNERSHIP_PERCENTAGE is null or zero, CAPITAL_CALLED is null or zero, NO_OF_SHARES is null or zero, COMMITTED_CAPITAL is null or zero, all fields are null indicating the file might be empty
2025-07-14 16:57:52,547 - INFO - Validation completed. Output saved to: data\outputs\FundHoldings_WithErrors_validated.xlsx
2025-07-14 17:01:26,367 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 17:01:26,367 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 17:01:26,367 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 17:01:26,554 - INFO - Number of rows to validate: 100
2025-07-14 17:01:26,554 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 17:01:28,267 - INFO - Row 2: OK - All validation rules have been passed for Row #2
2025-07-14 17:01:30,977 - INFO - Row 3: OK - All validation rules have been met for Row #3.
2025-07-14 17:01:32,292 - INFO - Row 4: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero
2025-07-14 17:01:33,864 - INFO - Row 5: OK - All validation rules have been met for Row #5.
2025-07-14 17:01:35,416 - INFO - Row 6: OK - All validation rules have been successfully applied and the row data meets the specified criteria.
2025-07-14 17:01:37,237 - INFO - Row 7: OK - All data fields adhere to the specified validation rules.
2025-07-14 17:01:38,628 - INFO - Row 8: OK - All data fields comply with the validation rules.
2025-07-14 17:01:40,382 - INFO - Row 9: FAIL - The COMMITTED_CAPITAL field should not be null or zero, but its value is significantly lower than expected when compared to NAV and CAPITAL_CALLED, indicating a potential data entry error or inconsistency.
2025-07-14 17:01:41,644 - INFO - Row 10: OK - All validation rules have been met for Row #10
2025-07-14 17:01:43,056 - INFO - Row 11: FAIL - UNIQUE_ID is null or empty which violates the null_check rule; UNIQUE_ID should not be null or empty.
2025-07-14 17:01:45,425 - INFO - Row 12: FAIL - UNIQUE_ID is null or empty which violates the null_check rule, and NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero but UNIQUE_ID is null.
2025-07-14 17:01:46,643 - INFO - Row 13: OK - All validation rules have been passed for Row #13
2025-07-14 17:01:48,310 - INFO - Row 14: OK - All data in the row passes the specified validation rules.
2025-07-14 17:01:50,008 - INFO - Row 15: OK - All validation rules have been successfully passed for Row #15.
2025-07-14 17:01:51,503 - INFO - Row 16: OK - All data fields are valid as per the given validation rules.
2025-07-14 17:01:52,946 - INFO - Row 17: OK - All validation rules have been passed for Row #17
2025-07-14 17:01:54,773 - INFO - Row 18: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero; it contains 'nan'.
2025-07-14 17:01:56,238 - INFO - Row 19: OK - All data in the row passes the specified validation rules.
2025-07-14 17:01:57,785 - INFO - Row 20: OK - All validation rules have been passed for row #20.
2025-07-14 17:01:58,891 - INFO - Row 21: OK - All validation rules have been applied and the row data meets the specified requirements.
2025-07-14 17:02:00,488 - INFO - Row 22: OK - All validation rules have been met for Row #22.
2025-07-14 17:02:02,270 - INFO - Row 23: OK - All validation rules have been successfully applied and the data row is correct.
2025-07-14 17:02:03,800 - INFO - Row 24: OK - All validation rules have been successfully applied and the data row is correct.
2025-07-14 17:02:05,437 - INFO - Row 25: OK - All validation rules have been passed for row #25.
2025-07-14 17:02:07,962 - INFO - Row 26: FAIL - UNIQUE_ID is null or empty, NAV is null or zero, OWNERSHIP_PERCENTAGE is null or zero, CAPITAL_CALLED is null or zero, NO_OF_SHARES is null or zero, COMMITTED_CAPITAL is null or zero, all fields are null indicating a possibly empty file or row
2025-07-14 17:02:10,187 - INFO - Row 27: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value
2025-07-14 17:02:11,629 - INFO - Row 28: OK - All validation rules have been passed
2025-07-14 17:02:13,048 - INFO - Row 29: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 17:02:14,595 - INFO - Row 30: OK - All data fields are valid as per the given validation rules.
2025-07-14 17:02:18,096 - INFO - Row 31: OK - All validation rules have been met for Row #31. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. Numeric fields contain valid numbers and no duplicates of UNIQUE_ID are mentioned.
2025-07-14 17:02:20,011 - INFO - Row 32: OK - All validation rules have been met for the given row data.
2025-07-14 17:02:21,658 - INFO - Row 33: OK - All validation rules have been successfully passed for row #33.
2025-07-14 17:02:23,409 - INFO - Row 34: FAIL - OWNERSHIP_PERCENTAGE is null, which violates the null_check rule
2025-07-14 17:02:24,885 - INFO - Row 35: OK - All validation rules have been passed for Row #35
2025-07-14 17:02:26,800 - INFO - Row 36: OK - All data in the row passes the specified validation rules.
2025-07-14 17:02:28,216 - INFO - Row 37: OK - All validation rules have been met for this row.
2025-07-14 17:02:29,814 - INFO - Row 38: OK - All validation rules have been successfully applied and the row data passes all checks.
2025-07-14 17:02:31,347 - INFO - Row 39: OK - All validation rules have been passed for Row #39
2025-07-14 17:02:32,991 - INFO - Row 40: OK - All validation rules and basic checks are passed.
2025-07-14 17:02:34,445 - INFO - Row 41: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 17:02:36,096 - INFO - Row 42: FAIL - OWNERSHIP_PERCENTAGE must be between 0 and 100
2025-07-14 17:02:37,478 - INFO - Row 43: FAIL - NAV should not be null or zero
2025-07-14 17:02:39,042 - INFO - Row 44: FAIL - NAV is null which violates the null_check rule
2025-07-14 17:02:41,848 - INFO - Row 45: FAIL - UNIQUE_ID is null or empty, NAV is null or zero, OWNERSHIP_PERCENTAGE is null or zero, CAPITAL_CALLED is null or zero, NO_OF_SHARES is null or zero, COMMITTED_CAPITAL is null or zero, and the entire row contains null values indicating an empty row which violates the business rule that the file should not be empty.
2025-07-14 17:02:43,535 - INFO - Row 46: OK - All validation rules have been met for this row.
2025-07-14 17:02:46,308 - INFO - Row 47: OK - All validation rules have been met for Row #47. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. All fields meet the basic validation requirements.
2025-07-14 17:02:47,752 - INFO - Row 48: OK - All validation rules have been passed for row #48
2025-07-14 17:02:49,663 - INFO - Row 49: FAIL - OWNERSHIP_PERCENTAGE is not a valid number between 0 and 100; it is NaN which violates the null_check rule.
2025-07-14 17:02:51,136 - INFO - Row 50: FAIL - OWNERSHIP_PERCENTAGE is not between 0 and 100
2025-07-14 17:02:52,303 - INFO - Row 51: OK - All validation rules passed
2025-07-14 17:02:53,613 - INFO - Row 52: FAIL - NAV is null which violates the null_check rule
2025-07-14 17:02:55,001 - INFO - Row 53: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero; it contains 'nan'.
2025-07-14 17:03:00,261 - INFO - Row 54: FAIL - UNIQUE_ID is null which violates the null_check rule. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL are non-null and non-zero which is correct. UNIQUE_ID does not contain alphanumeric characters and starts with 'TOTAL' which violates the format_check rule. There is no duplicate UNIQUE_ID in the provided data. The file is not empty. Data types for each column are correct. Numeric fields contain valid numbers. Percentage value for OWNERSHIP_PERCENTAGE is between 0 and 100. Required fields are not null except for UNIQUE_ID. Dates are in standard format. Text fields do not have invalid characters. Business logic is followed as there are no negative values where not expected.
2025-07-14 17:03:01,668 - INFO - Row 55: OK - All validation rules are satisfied for Row #55
2025-07-14 17:03:02,919 - INFO - Row 56: OK - All validation rules have been successfully applied and the data row is correct.
2025-07-14 17:03:04,030 - INFO - Row 57: OK - All validation checks passed
2025-07-14 17:03:05,417 - INFO - Row 58: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 17:03:07,013 - INFO - Row 59: OK - All validation rules passed
2025-07-14 17:03:08,711 - INFO - Row 60: OK - All validation rules have been passed for Row #60
2025-07-14 17:03:11,770 - INFO - Row 61: OK - All validation rules passed. The data types are correct, no null or zero values in specified fields, UNIQUE_ID does not start with 'TOTAL' and contains only alphanumeric characters, no empty file or fields, UNIQUE_ID is not a duplicate, numeric fields contain valid numbers, percentage is within 0-100, and all business logic conditions are met.
2025-07-14 17:03:13,562 - INFO - Row 62: FAIL - OWNERSHIP_PERCENTAGE must be between 0 and 100
2025-07-14 17:03:15,223 - INFO - Row 63: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100
2025-07-14 17:03:16,700 - INFO - Row 64: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 17:03:18,299 - INFO - Row 65: OK - All validation rules have been successfully applied and the row data meets the specified criteria.
2025-07-14 17:03:19,389 - INFO - Row 66: OK - All validation rules have been passed
2025-07-14 17:03:20,880 - INFO - Row 67: FAIL - OWNERSHIP_PERCENTAGE must be between 0 and 100
2025-07-14 17:03:22,513 - INFO - Row 68: OK - All validation rules have been passed for Row #68
2025-07-14 17:03:24,271 - INFO - Row 69: OK - All validation rules passed for row #69
2025-07-14 17:03:25,607 - INFO - Row 70: OK - All data fields are valid according to the specified validation rules.
2025-07-14 17:03:27,430 - INFO - Row 71: FAIL - UNIQUE_ID starts with 'TOTAL' which is against the format_check rule
2025-07-14 17:03:28,997 - INFO - Row 72: OK - All validation rules have been applied and the data in Row #72 is correct.
2025-07-14 17:03:30,255 - INFO - Row 73: OK - All data fields are valid according to the specified validation rules.
2025-07-14 17:03:32,100 - INFO - Row 74: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 17:03:33,945 - INFO - Row 75: FAIL - UNIQUE_ID is null or empty which violates the null_check rule, and UNIQUE_ID should not be null or empty as per the basic validations.
2025-07-14 17:03:35,318 - INFO - Row 76: OK - All validation rules have been successfully applied and the row data meets the specified criteria.
2025-07-14 17:03:36,686 - INFO - Row 77: OK - All validation rules have been applied and the data in Row #77 meets the specified criteria.
2025-07-14 17:03:38,491 - INFO - Row 78: FAIL - COMMITTED_CAPITAL should not be null or zero but the provided value is unreasonably low compared to other financial figures.
2025-07-14 17:03:40,060 - INFO - Row 79: OK - All validation rules have been met for Row #79
2025-07-14 17:03:41,742 - INFO - Row 80: OK - All validation rules have been met for Row #80
2025-07-14 17:03:42,913 - INFO - Row 81: OK - All validation rules have been passed for Row #81
2025-07-14 17:03:44,338 - INFO - Row 82: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value
2025-07-14 17:03:45,564 - INFO - Row 83: FAIL - NAV is null which violates the null_check rule
2025-07-14 17:03:47,106 - INFO - Row 84: FAIL - OWNERSHIP_PERCENTAGE is out of expected range 0-100
2025-07-14 17:03:48,622 - INFO - Row 85: OK - All validation rules have been applied and the data row is correct.
2025-07-14 17:03:50,213 - INFO - Row 86: OK - All validation rules have been passed for Row #86
2025-07-14 17:03:52,245 - INFO - Row 87: OK - All data in the row meets the specified validation rules and basic checks.
2025-07-14 17:03:53,509 - INFO - Row 88: OK - All validation rules have been successfully passed for row #88.
2025-07-14 17:03:54,751 - INFO - Row 89: OK - All validation rules have been passed for Row #89
2025-07-14 17:03:56,286 - INFO - Row 90: FAIL - UNIQUE_ID is null or empty which violates the null_check rule; UNIQUE_ID should not be null or empty.
2025-07-14 17:03:58,502 - INFO - Row 91: FAIL - The COMMITTED_CAPITAL field should not be null or zero but the value provided is less than the CAPITAL_CALLED which is not reasonable as COMMITTED_CAPITAL should be greater or equal to CAPITAL_CALLED.
2025-07-14 17:03:59,749 - INFO - Row 92: FAIL - NAV should not be null or zero
2025-07-14 17:04:03,145 - INFO - Row 93: OK - All validation rules have been met for Row #93. UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. All fields contain valid data types and values as per the given rules.
2025-07-14 17:04:04,433 - INFO - Row 94: OK - All validation rules have been passed for Row #94
2025-07-14 17:04:06,094 - INFO - Row 95: FAIL - The NO_OF_SHARES field contains a decimal value which is not valid for a count of shares. Shares should be represented as whole numbers.
2025-07-14 17:04:07,504 - INFO - Row 96: OK - All data in the row passes the specific validation rules and basic checks.
2025-07-14 17:04:11,033 - INFO - Row 97: OK - All validation rules passed. The UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. The OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. There are no indications of duplicate UNIQUE_ID, invalid data types, or any other validation rule being broken.
2025-07-14 17:04:14,644 - INFO - Row 98: FAIL - Row contains null values for UNIQUE_ID, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL which are not allowed as per null_check rule. UNIQUE_ID is also null which violates the null_check rule for UNIQUE_ID. The entire row is null which violates the business rule that the file should not be empty.
2025-07-14 17:04:17,747 - INFO - Row 99: OK - All validation rules have been met for Row #99. The UNIQUE_ID does not start with 'TOTAL', contains only alphanumeric characters, and is not null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, and COMMITTED_CAPITAL are not null or zero. OWNERSHIP_PERCENTAGE is within the valid range of 0 to 100. All fields are of the correct data type and contain valid values.
2025-07-14 17:04:19,278 - INFO - Row 100: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value.
2025-07-14 17:04:21,005 - INFO - Row 101: FAIL - OWNERSHIP_PERCENTAGE is greater than 100 which is not valid for a percentage value
2025-07-14 17:04:21,058 - INFO - Validation completed. Output saved to: data\outputs\FundHoldings_WithErrors_validated.xlsx
2025-07-14 17:25:18,714 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 17:25:18,715 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 17:25:18,715 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 17:25:18,899 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 17:25:21,989 - INFO - Row 2: OK - Row data is valid
2025-07-14 17:25:21,990 - INFO - Row 3: FAIL - OWNERSHIP_PERCENTAGE is not a valid number
2025-07-14 17:25:21,990 - INFO - Row 4: OK - Row data is valid
2025-07-14 17:25:21,991 - INFO - Row 5: FAIL - Sum of OWNERSHIP_PERCENTAGE across all rows does not equal 100%
2025-07-14 17:25:24,415 - INFO - Row 7: OK - 
2025-07-14 17:25:24,415 - INFO - Row 8: OK - 
2025-07-14 17:25:24,415 - INFO - Row 9: OK - 
2025-07-14 17:25:24,415 - INFO - Row 10: OK - 
2025-07-14 17:25:24,415 - INFO - Row 11: FAIL - UNIQUE_ID is null or empty
2025-07-14 17:25:27,490 - INFO - Row 12: FAIL - UNIQUE_ID is null or empty.
2025-07-14 17:25:27,490 - INFO - Row 13: OK - 
2025-07-14 17:25:27,490 - INFO - Row 14: OK - 
2025-07-14 17:25:27,490 - INFO - Row 15: OK - 
2025-07-14 17:25:27,490 - INFO - Row 16: OK - 
2025-07-14 17:25:30,402 - INFO - Row 17: OK - 
2025-07-14 17:25:30,404 - INFO - Row 18: FAIL - OWNERSHIP_PERCENTAGE is null
2025-07-14 17:25:30,404 - INFO - Row 19: OK - 
2025-07-14 17:25:30,404 - INFO - Row 20: OK - 
2025-07-14 17:25:30,404 - INFO - Row 21: FAIL - Sum of OWNERSHIP_PERCENTAGE for all rows does not equal 100%
2025-07-14 17:25:34,701 - INFO - Row 22: OK - 
2025-07-14 17:25:34,701 - INFO - Row 23: OK - 
2025-07-14 17:25:34,701 - INFO - Row 24: OK - 
2025-07-14 17:25:34,701 - INFO - Row 25: OK - 
2025-07-14 17:25:34,702 - INFO - Row 26: FAIL - UNIQUE_ID is null or empty; NAV is null or zero; OWNERSHIP_PERCENTAGE is null or zero; CAPITAL_CALLED is null or zero; NO_OF_SHARES is null or zero; COMMITTED_CAPITAL is null or zero; PORTFOLIO_ID is null or empty; REGISTERED_HOLDER is null or empty; PERIOD is null or empty; FUND_NAME is null or empty
2025-07-14 17:25:38,568 - INFO - Row 27: FAIL - OWNERSHIP_PERCENTAGE sum is not 100%
2025-07-14 17:25:38,569 - INFO - Row 28: FAIL - OWNERSHIP_PERCENTAGE sum is not 100%
2025-07-14 17:25:38,569 - INFO - Row 29: FAIL - OWNERSHIP_PERCENTAGE sum is not 100%
2025-07-14 17:25:38,569 - INFO - Row 30: FAIL - OWNERSHIP_PERCENTAGE sum is not 100%
2025-07-14 17:25:38,569 - INFO - Row 31: FAIL - OWNERSHIP_PERCENTAGE sum is not 100%
2025-07-14 17:25:41,519 - INFO - Row 32: OK - Row data is valid according to the given rules.
2025-07-14 17:25:41,519 - INFO - Row 33: OK - Row data is valid according to the given rules.
2025-07-14 17:25:41,520 - INFO - Row 34: FAIL - OWNERSHIP_PERCENTAGE is null which violates the null_check rule.
2025-07-14 17:25:41,520 - INFO - Row 35: OK - Row data is valid according to the given rules.
2025-07-14 17:25:41,520 - INFO - Row 36: OK - Row data is valid according to the given rules.
2025-07-14 17:25:43,564 - INFO - Row 37: OK - 
2025-07-14 17:25:43,564 - INFO - Row 38: OK - 
2025-07-14 17:25:43,564 - INFO - Row 39: OK - 
2025-07-14 17:25:43,565 - INFO - Row 40: OK - 
2025-07-14 17:25:43,565 - INFO - Row 41: FAIL - The sum of OWNERSHIP_PERCENTAGE across all rows does not equal 100%
2025-07-14 17:25:48,961 - INFO - Row 42: OK - Row data is valid according to the validation rules.
2025-07-14 17:25:48,961 - INFO - Row 43: FAIL - NAV is null which violates the null_check rule.
2025-07-14 17:25:48,961 - INFO - Row 44: FAIL - NAV is null which violates the null_check rule.
2025-07-14 17:25:48,961 - INFO - Row 45: FAIL - UNIQUE_ID, PORTFOLIO_ID, REGISTERED_HOLDER, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL, PERIOD, and FUND_NAME are null which violates the null_check rule.
2025-07-14 17:25:48,963 - INFO - Row 46: OK - Row data is valid according to the validation rules.
2025-07-14 17:25:48,963 - INFO - Row 47: FAIL - The sum of OWNERSHIP_PERCENTAGE across all rows does not equal 100%, violating the special business rule.
2025-07-14 17:25:52,176 - INFO - Row 47: OK - 
2025-07-14 17:25:52,176 - INFO - Row 48: OK - 
2025-07-14 17:25:52,177 - INFO - Row 49: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero
2025-07-14 17:25:52,178 - INFO - Row 50: OK - 
2025-07-14 17:25:52,178 - INFO - Row 51: FAIL - Sum of OWNERSHIP_PERCENTAGE for all rows does not equal 100%
2025-07-14 17:25:55,695 - INFO - Row 52: FAIL - NAV should not be null or zero.
2025-07-14 17:25:55,695 - INFO - Row 53: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero.
2025-07-14 17:25:55,695 - INFO - Row 54: FAIL - UNIQUE_ID should not be null or empty.
2025-07-14 17:25:55,696 - INFO - Row 55: FAIL - OWNERSHIP_PERCENTAGE sum across all rows is not exactly 100%.
2025-07-14 17:25:55,696 - INFO - Row 56: FAIL - OWNERSHIP_PERCENTAGE sum across all rows is not exactly 100%.
2025-07-14 17:25:59,230 - INFO - Row 57: OK - 
2025-07-14 17:25:59,231 - INFO - Row 58: OK - 
2025-07-14 17:25:59,231 - INFO - Row 59: OK - 
2025-07-14 17:25:59,232 - INFO - Row 60: FAIL - The sum of the column OWNERSHIP_PERCENTAGE for all rows in the batch does not equal 100%.
2025-07-14 17:25:59,233 - INFO - Row 61: FAIL - The sum of the column OWNERSHIP_PERCENTAGE for all rows in the batch does not equal 100%.
2025-07-14 17:26:02,760 - INFO - Row 62: OK - 
2025-07-14 17:26:02,760 - INFO - Row 63: OK - 
2025-07-14 17:26:02,761 - INFO - Row 64: OK - 
2025-07-14 17:26:02,761 - INFO - Row 65: FAIL - The sum of OWNERSHIP_PERCENTAGE across all rows does not equal 100%.
2025-07-14 17:26:02,761 - INFO - Row 66: FAIL - The sum of OWNERSHIP_PERCENTAGE across all rows does not equal 100%.
2025-07-14 17:26:05,937 - INFO - Row 67: OK - Row data is valid
2025-07-14 17:26:05,937 - INFO - Row 68: OK - Row data is valid
2025-07-14 17:26:05,937 - INFO - Row 69: OK - Row data is valid
2025-07-14 17:26:05,937 - INFO - Row 70: OK - Row data is valid
2025-07-14 17:26:05,938 - INFO - Row 71: FAIL - The sum of OWNERSHIP_PERCENTAGE across all rows does not equal 100%
2025-07-14 17:26:08,954 - INFO - Row 72: OK - 
2025-07-14 17:26:08,954 - INFO - Row 73: OK - 
2025-07-14 17:26:08,955 - INFO - Row 74: OK - 
2025-07-14 17:26:08,955 - INFO - Row 75: FAIL - UNIQUE_ID is null or empty.
2025-07-14 17:26:08,955 - INFO - Row 76: OK - 
2025-07-14 17:26:08,956 - INFO - Row 77: FAIL - The sum of OWNERSHIP_PERCENTAGE across all rows does not equal 100%.
2025-07-14 17:26:11,630 - INFO - Row 77: OK - 
2025-07-14 17:26:11,630 - INFO - Row 78: OK - 
2025-07-14 17:26:11,630 - INFO - Row 79: OK - 
2025-07-14 17:26:11,632 - INFO - Row 80: OK - 
2025-07-14 17:26:11,632 - INFO - Row 81: FAIL - The sum of OWNERSHIP_PERCENTAGE across all rows does not equal 100%
2025-07-14 17:26:14,464 - INFO - Row 82: OK - All checks passed
2025-07-14 17:26:14,464 - INFO - Row 83: FAIL - NAV is null
2025-07-14 17:26:14,464 - INFO - Row 84: OK - All checks passed
2025-07-14 17:26:14,465 - INFO - Row 85: OK - All checks passed
2025-07-14 17:26:14,465 - INFO - Row 86: OK - All checks passed
2025-07-14 17:26:18,142 - INFO - Row 87: OK - Row data is valid according to the specified rules.
2025-07-14 17:26:18,143 - INFO - Row 88: OK - Row data is valid according to the specified rules.
2025-07-14 17:26:18,143 - INFO - Row 89: OK - Row data is valid according to the specified rules.
2025-07-14 17:26:18,143 - INFO - Row 90: FAIL - UNIQUE_ID is null or empty.
2025-07-14 17:26:18,143 - INFO - Row 91: FAIL - The sum of OWNERSHIP_PERCENTAGE for all rows does not equal 100%.
2025-07-14 17:26:20,289 - INFO - Row 92: FAIL - NAV is null
2025-07-14 17:26:20,289 - INFO - Row 93: OK - 
2025-07-14 17:26:20,291 - INFO - Row 94: OK - 
2025-07-14 17:26:20,291 - INFO - Row 95: OK - 
2025-07-14 17:26:20,291 - INFO - Row 96: OK - 
2025-07-14 17:26:24,791 - INFO - Row 97: OK - Row data is valid according to the validation rules.
2025-07-14 17:26:24,791 - INFO - Row 98: FAIL - UNIQUE_ID is null or empty. NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero.
2025-07-14 17:26:24,791 - INFO - Row 99: OK - Row data is valid according to the validation rules.
2025-07-14 17:26:24,792 - INFO - Row 100: OK - Row data is valid according to the validation rules.
2025-07-14 17:26:24,792 - INFO - Row 101: FAIL - The sum of OWNERSHIP_PERCENTAGE for all rows does not equal 100%.
2025-07-14 17:26:24,853 - INFO - Excel saved to: data\outputs\FundHoldings_WithErrors_validated.xlsx
2025-07-14 17:26:24,948 - INFO - JSON saved to: data\outputs\FundHoldings_WithErrors_validated.json
2025-07-14 17:35:13,543 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 17:35:13,545 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 17:35:13,545 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 17:35:13,743 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 17:35:13,746 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,746 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 230.64 for rows 2 to 6
2025-07-14 17:35:13,746 - INFO - Row 2: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,747 - INFO - Row 3: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,747 - INFO - Row 4: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,747 - INFO - Row 5: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,747 - INFO - Row 6: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,748 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,749 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 189.93 for rows 7 to 11
2025-07-14 17:35:13,749 - INFO - Row 7: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,750 - INFO - Row 8: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,750 - INFO - Row 9: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,750 - INFO - Row 10: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,750 - INFO - Row 11: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,753 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,753 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 271.45 for rows 12 to 16
2025-07-14 17:35:13,753 - INFO - Row 12: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,754 - INFO - Row 13: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,754 - INFO - Row 14: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,754 - INFO - Row 15: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,754 - INFO - Row 16: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,761 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,762 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 255.77 for rows 17 to 21
2025-07-14 17:35:13,762 - INFO - Row 17: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,762 - INFO - Row 18: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,762 - INFO - Row 19: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,762 - INFO - Row 20: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,764 - INFO - Row 21: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,779 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,783 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 180.62 for rows 22 to 26
2025-07-14 17:35:13,785 - INFO - Row 22: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,785 - INFO - Row 23: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,787 - INFO - Row 24: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,787 - INFO - Row 25: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,788 - INFO - Row 26: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,793 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,794 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 261.95 for rows 27 to 31
2025-07-14 17:35:13,796 - INFO - Row 27: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,796 - INFO - Row 28: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,800 - INFO - Row 29: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,802 - INFO - Row 30: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,802 - INFO - Row 31: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,804 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,804 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 228.38 for rows 32 to 36
2025-07-14 17:35:13,814 - INFO - Row 32: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,814 - INFO - Row 33: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,816 - INFO - Row 34: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,816 - INFO - Row 35: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,816 - INFO - Row 36: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,818 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,820 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 231.09 for rows 37 to 41
2025-07-14 17:35:13,820 - INFO - Row 37: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,820 - INFO - Row 38: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,822 - INFO - Row 39: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,822 - INFO - Row 40: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,822 - INFO - Row 41: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,824 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,824 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 152.88 for rows 42 to 46
2025-07-14 17:35:13,826 - INFO - Row 42: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,826 - INFO - Row 43: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,826 - INFO - Row 44: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,826 - INFO - Row 45: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,826 - INFO - Row 46: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,829 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,829 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 214.25 for rows 47 to 51
2025-07-14 17:35:13,831 - INFO - Row 47: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,831 - INFO - Row 48: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,831 - INFO - Row 49: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,831 - INFO - Row 50: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,831 - INFO - Row 51: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,833 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,833 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 246.36 for rows 52 to 56
2025-07-14 17:35:13,833 - INFO - Row 52: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,835 - INFO - Row 53: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,835 - INFO - Row 54: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,835 - INFO - Row 55: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,835 - INFO - Row 56: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,837 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,839 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 135.81 for rows 57 to 61
2025-07-14 17:35:13,842 - INFO - Row 57: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,842 - INFO - Row 58: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,844 - INFO - Row 59: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,844 - INFO - Row 60: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,844 - INFO - Row 61: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,853 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,854 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 225.60 for rows 62 to 66
2025-07-14 17:35:13,855 - INFO - Row 62: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,855 - INFO - Row 63: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,855 - INFO - Row 64: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,855 - INFO - Row 65: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,857 - INFO - Row 66: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,857 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,859 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 197.14 for rows 67 to 71
2025-07-14 17:35:13,859 - INFO - Row 67: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,859 - INFO - Row 68: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,859 - INFO - Row 69: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,859 - INFO - Row 70: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,861 - INFO - Row 71: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,864 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,866 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 232.21 for rows 72 to 76
2025-07-14 17:35:13,868 - INFO - Row 72: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,868 - INFO - Row 73: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,868 - INFO - Row 74: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,870 - INFO - Row 75: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,870 - INFO - Row 76: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,872 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,872 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 156.73 for rows 77 to 81
2025-07-14 17:35:13,872 - INFO - Row 77: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,873 - INFO - Row 78: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,873 - INFO - Row 79: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,873 - INFO - Row 80: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,873 - INFO - Row 81: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,875 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,875 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 243.90 for rows 82 to 86
2025-07-14 17:35:13,877 - INFO - Row 82: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,877 - INFO - Row 83: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,877 - INFO - Row 84: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,877 - INFO - Row 85: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,877 - INFO - Row 86: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,879 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,879 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 180.65 for rows 87 to 91
2025-07-14 17:35:13,879 - INFO - Row 87: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,879 - INFO - Row 88: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,879 - INFO - Row 89: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,881 - INFO - Row 90: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,881 - INFO - Row 91: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,883 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,887 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 247.28 for rows 92 to 96
2025-07-14 17:35:13,889 - INFO - Row 92: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,890 - INFO - Row 93: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,890 - INFO - Row 94: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,890 - INFO - Row 95: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,890 - INFO - Row 96: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,892 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:35:13,892 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 181.95 for rows 97 to 101
2025-07-14 17:35:13,892 - INFO - Row 97: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,892 - INFO - Row 98: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,894 - INFO - Row 99: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,894 - INFO - Row 100: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,894 - INFO - Row 101: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:35:13,974 - INFO - Excel saved to: data\outputs\FundHoldings_WithErrors_validated.xlsx
2025-07-14 17:35:13,979 - INFO - JSON saved to: data\outputs\FundHoldings_WithErrors_validated.json
2025-07-14 17:38:09,585 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 17:38:09,586 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 17:38:09,586 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 17:38:09,767 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 17:38:09,767 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,767 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 230.64 for rows 2 to 6
2025-07-14 17:38:09,767 - INFO - Row 2: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,767 - INFO - Row 3: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,783 - INFO - Row 4: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,783 - INFO - Row 5: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,783 - INFO - Row 6: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,785 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,786 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 189.93 for rows 7 to 11
2025-07-14 17:38:09,786 - INFO - Row 7: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,787 - INFO - Row 8: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,787 - INFO - Row 9: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,787 - INFO - Row 10: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,788 - INFO - Row 11: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,789 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,789 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 271.45 for rows 12 to 16
2025-07-14 17:38:09,790 - INFO - Row 12: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,790 - INFO - Row 13: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,790 - INFO - Row 14: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,790 - INFO - Row 15: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,791 - INFO - Row 16: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,792 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,792 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 255.77 for rows 17 to 21
2025-07-14 17:38:09,792 - INFO - Row 17: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,792 - INFO - Row 18: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,793 - INFO - Row 19: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,794 - INFO - Row 20: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,794 - INFO - Row 21: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,796 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,798 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 180.62 for rows 22 to 26
2025-07-14 17:38:09,798 - INFO - Row 22: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,799 - INFO - Row 23: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,800 - INFO - Row 24: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,800 - INFO - Row 25: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,801 - INFO - Row 26: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,804 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,805 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 261.95 for rows 27 to 31
2025-07-14 17:38:09,805 - INFO - Row 27: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,806 - INFO - Row 28: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,806 - INFO - Row 29: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,806 - INFO - Row 30: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,806 - INFO - Row 31: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,808 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,809 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 228.38 for rows 32 to 36
2025-07-14 17:38:09,809 - INFO - Row 32: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,809 - INFO - Row 33: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,810 - INFO - Row 34: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,810 - INFO - Row 35: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,810 - INFO - Row 36: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,811 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,812 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 231.09 for rows 37 to 41
2025-07-14 17:38:09,812 - INFO - Row 37: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,812 - INFO - Row 38: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,812 - INFO - Row 39: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,812 - INFO - Row 40: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,813 - INFO - Row 41: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,813 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,814 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 152.88 for rows 42 to 46
2025-07-14 17:38:09,814 - INFO - Row 42: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,814 - INFO - Row 43: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,815 - INFO - Row 44: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,815 - INFO - Row 45: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,815 - INFO - Row 46: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,815 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,815 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 214.25 for rows 47 to 51
2025-07-14 17:38:09,819 - INFO - Row 47: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,819 - INFO - Row 48: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,819 - INFO - Row 49: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,819 - INFO - Row 50: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,819 - INFO - Row 51: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,822 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,822 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 246.36 for rows 52 to 56
2025-07-14 17:38:09,828 - INFO - Row 52: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,828 - INFO - Row 53: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,828 - INFO - Row 54: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,828 - INFO - Row 55: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,830 - INFO - Row 56: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,830 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,830 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 135.81 for rows 57 to 61
2025-07-14 17:38:09,830 - INFO - Row 57: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,830 - INFO - Row 58: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,830 - INFO - Row 59: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,830 - INFO - Row 60: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,830 - INFO - Row 61: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,830 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,830 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 225.60 for rows 62 to 66
2025-07-14 17:38:09,838 - INFO - Row 62: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,840 - INFO - Row 63: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,840 - INFO - Row 64: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,840 - INFO - Row 65: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,842 - INFO - Row 66: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,842 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,842 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 197.14 for rows 67 to 71
2025-07-14 17:38:09,845 - INFO - Row 67: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,845 - INFO - Row 68: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,845 - INFO - Row 69: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,846 - INFO - Row 70: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,846 - INFO - Row 71: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,848 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,849 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 232.21 for rows 72 to 76
2025-07-14 17:38:09,849 - INFO - Row 72: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,852 - INFO - Row 73: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,852 - INFO - Row 74: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,852 - INFO - Row 75: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,854 - INFO - Row 76: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,855 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,855 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 156.73 for rows 77 to 81
2025-07-14 17:38:09,857 - INFO - Row 77: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,857 - INFO - Row 78: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,858 - INFO - Row 79: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,858 - INFO - Row 80: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,858 - INFO - Row 81: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,860 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,860 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 243.90 for rows 82 to 86
2025-07-14 17:38:09,861 - INFO - Row 82: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,861 - INFO - Row 83: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,861 - INFO - Row 84: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,861 - INFO - Row 85: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,861 - INFO - Row 86: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,861 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,861 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 180.65 for rows 87 to 91
2025-07-14 17:38:09,861 - INFO - Row 87: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,861 - INFO - Row 88: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,861 - INFO - Row 89: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,861 - INFO - Row 90: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,861 - INFO - Row 91: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,868 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,868 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 247.28 for rows 92 to 96
2025-07-14 17:38:09,870 - INFO - Row 92: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,870 - INFO - Row 93: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,872 - INFO - Row 94: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,872 - INFO - Row 95: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,872 - INFO - Row 96: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,876 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:38:09,876 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 181.95 for rows 97 to 101
2025-07-14 17:38:09,876 - INFO - Row 97: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,876 - INFO - Row 98: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,876 - INFO - Row 99: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,876 - INFO - Row 100: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,876 - INFO - Row 101: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:38:09,964 - INFO - Excel saved to: data\outputs\FundHoldings_WithErrors_validated.xlsx
2025-07-14 17:38:09,967 - INFO - JSON saved to: data\outputs\FundHoldings_WithErrors_validated.json
2025-07-14 17:40:24,967 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 17:40:24,968 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 17:40:24,968 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 17:40:25,146 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 17:40:25,146 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,146 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 230.64 for rows 2 to 6
2025-07-14 17:40:25,146 - INFO - Row 2: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,146 - INFO - Row 3: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,146 - INFO - Row 4: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,146 - INFO - Row 5: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,146 - INFO - Row 6: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,153 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,153 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 189.93 for rows 7 to 11
2025-07-14 17:40:25,153 - INFO - Row 7: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,153 - INFO - Row 8: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,154 - INFO - Row 9: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,154 - INFO - Row 10: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,154 - INFO - Row 11: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,156 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,157 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 271.45 for rows 12 to 16
2025-07-14 17:40:25,157 - INFO - Row 12: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,157 - INFO - Row 13: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,157 - INFO - Row 14: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,158 - INFO - Row 15: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,158 - INFO - Row 16: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,159 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,160 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 255.77 for rows 17 to 21
2025-07-14 17:40:25,160 - INFO - Row 17: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,162 - INFO - Row 18: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,162 - INFO - Row 19: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,163 - INFO - Row 20: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,163 - INFO - Row 21: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,165 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,165 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 180.62 for rows 22 to 26
2025-07-14 17:40:25,166 - INFO - Row 22: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,166 - INFO - Row 23: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,167 - INFO - Row 24: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,167 - INFO - Row 25: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,167 - INFO - Row 26: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,171 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,171 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 261.95 for rows 27 to 31
2025-07-14 17:40:25,171 - INFO - Row 27: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,171 - INFO - Row 28: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,171 - INFO - Row 29: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,173 - INFO - Row 30: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,173 - INFO - Row 31: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,174 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,175 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 228.38 for rows 32 to 36
2025-07-14 17:40:25,176 - INFO - Row 32: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,176 - INFO - Row 33: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,176 - INFO - Row 34: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,176 - INFO - Row 35: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,177 - INFO - Row 36: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,178 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,178 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 231.09 for rows 37 to 41
2025-07-14 17:40:25,179 - INFO - Row 37: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,179 - INFO - Row 38: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,179 - INFO - Row 39: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,179 - INFO - Row 40: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,179 - INFO - Row 41: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,179 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,179 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 152.88 for rows 42 to 46
2025-07-14 17:40:25,182 - INFO - Row 42: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,182 - INFO - Row 43: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,182 - INFO - Row 44: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,182 - INFO - Row 45: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,182 - INFO - Row 46: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,184 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,184 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 214.25 for rows 47 to 51
2025-07-14 17:40:25,184 - INFO - Row 47: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,186 - INFO - Row 48: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,186 - INFO - Row 49: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,186 - INFO - Row 50: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,186 - INFO - Row 51: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,192 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,192 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 246.36 for rows 52 to 56
2025-07-14 17:40:25,194 - INFO - Row 52: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,194 - INFO - Row 53: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,196 - INFO - Row 54: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,197 - INFO - Row 55: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,197 - INFO - Row 56: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,199 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,199 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 135.81 for rows 57 to 61
2025-07-14 17:40:25,199 - INFO - Row 57: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,199 - INFO - Row 58: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,199 - INFO - Row 59: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,201 - INFO - Row 60: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,201 - INFO - Row 61: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,203 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,205 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 225.60 for rows 62 to 66
2025-07-14 17:40:25,207 - INFO - Row 62: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,207 - INFO - Row 63: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,208 - INFO - Row 64: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,208 - INFO - Row 65: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,208 - INFO - Row 66: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,209 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,210 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 197.14 for rows 67 to 71
2025-07-14 17:40:25,210 - INFO - Row 67: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,210 - INFO - Row 68: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,211 - INFO - Row 69: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,211 - INFO - Row 70: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,212 - INFO - Row 71: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,212 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,214 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 232.21 for rows 72 to 76
2025-07-14 17:40:25,214 - INFO - Row 72: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,216 - INFO - Row 73: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,216 - INFO - Row 74: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,216 - INFO - Row 75: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,218 - INFO - Row 76: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,220 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,222 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 156.73 for rows 77 to 81
2025-07-14 17:40:25,223 - INFO - Row 77: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,223 - INFO - Row 78: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,223 - INFO - Row 79: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,223 - INFO - Row 80: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,224 - INFO - Row 81: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,225 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,225 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 243.90 for rows 82 to 86
2025-07-14 17:40:25,226 - INFO - Row 82: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,226 - INFO - Row 83: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,226 - INFO - Row 84: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,227 - INFO - Row 85: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,227 - INFO - Row 86: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,229 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,230 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 180.65 for rows 87 to 91
2025-07-14 17:40:25,235 - INFO - Row 87: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,237 - INFO - Row 88: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,237 - INFO - Row 89: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,237 - INFO - Row 90: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,237 - INFO - Row 91: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,238 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,238 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 247.28 for rows 92 to 96
2025-07-14 17:40:25,239 - INFO - Row 92: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,239 - INFO - Row 93: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,239 - INFO - Row 94: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,240 - INFO - Row 95: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,240 - INFO - Row 96: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,241 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:25,241 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 181.95 for rows 97 to 101
2025-07-14 17:40:25,241 - INFO - Row 97: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,243 - INFO - Row 98: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,243 - INFO - Row 99: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,243 - INFO - Row 100: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,243 - INFO - Row 101: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:25,328 - INFO - Excel saved to: data\outputs\FundHoldings_WithErrors_validated.xlsx
2025-07-14 17:40:25,333 - INFO - JSON saved to: data\outputs\FundHoldings_WithErrors_validated.json
2025-07-14 17:40:56,996 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 17:40:56,996 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 17:40:56,997 - INFO - Input file path: data\inputs\Fundholding_Data_1 1.xlsx
2025-07-14 17:40:57,241 - INFO - Started validation for file: data\inputs\Fundholding_Data_1 1.xlsx with 29 rows.
2025-07-14 17:40:57,252 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:57,252 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 0.00 for rows 2 to 6
2025-07-14 17:40:57,252 - INFO - Row 2: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,252 - INFO - Row 3: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,255 - INFO - Row 4: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,255 - INFO - Row 5: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,255 - INFO - Row 6: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,257 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:57,257 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 0.00 for rows 7 to 11
2025-07-14 17:40:57,258 - INFO - Row 7: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,258 - INFO - Row 8: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,258 - INFO - Row 9: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,258 - INFO - Row 10: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,258 - INFO - Row 11: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,260 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:57,261 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 0.00 for rows 12 to 16
2025-07-14 17:40:57,262 - INFO - Row 12: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,262 - INFO - Row 13: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,263 - INFO - Row 14: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,263 - INFO - Row 15: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,263 - INFO - Row 16: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,265 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:57,266 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 0.00 for rows 17 to 21
2025-07-14 17:40:57,266 - INFO - Row 17: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,267 - INFO - Row 18: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,267 - INFO - Row 19: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,269 - INFO - Row 20: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,269 - INFO - Row 21: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,269 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:57,269 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 0.00 for rows 22 to 26
2025-07-14 17:40:57,271 - INFO - Row 22: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,271 - INFO - Row 23: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,271 - INFO - Row 24: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,271 - INFO - Row 25: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,271 - INFO - Row 26: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,273 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:40:57,274 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 0.00 for rows 27 to 30
2025-07-14 17:40:57,274 - INFO - Row 27: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,275 - INFO - Row 28: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,275 - INFO - Row 29: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,275 - INFO - Row 30: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:40:57,308 - INFO - Excel saved to: data\outputs\Fundholding_Data_1 1_validated.xlsx
2025-07-14 17:40:57,313 - INFO - JSON saved to: data\outputs\Fundholding_Data_1 1_validated.json
2025-07-14 17:41:16,547 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 17:41:16,547 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 17:41:16,548 - INFO - Input file path: data\inputs\Fundholding_Data _2 1.xlsx
2025-07-14 17:41:16,725 - INFO - Started validation for file: data\inputs\Fundholding_Data _2 1.xlsx with 29 rows.
2025-07-14 17:41:16,725 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:41:16,725 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 24.00 for rows 2 to 6
2025-07-14 17:41:16,725 - INFO - Row 2: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,725 - INFO - Row 3: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,725 - INFO - Row 4: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,725 - INFO - Row 5: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,725 - INFO - Row 6: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,725 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:41:16,725 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 22.00 for rows 7 to 11
2025-07-14 17:41:16,725 - INFO - Row 7: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,725 - INFO - Row 8: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,725 - INFO - Row 9: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,737 - INFO - Row 10: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,737 - INFO - Row 11: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,738 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:41:16,738 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 19.00 for rows 12 to 16
2025-07-14 17:41:16,738 - INFO - Row 12: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,739 - INFO - Row 13: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,739 - INFO - Row 14: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,739 - INFO - Row 15: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,739 - INFO - Row 16: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,740 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:41:16,740 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 10.00 for rows 17 to 21
2025-07-14 17:41:16,742 - INFO - Row 17: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,742 - INFO - Row 18: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,743 - INFO - Row 19: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,743 - INFO - Row 20: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,745 - INFO - Row 21: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,747 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:41:16,747 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 19.00 for rows 22 to 26
2025-07-14 17:41:16,748 - INFO - Row 22: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,748 - INFO - Row 23: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,749 - INFO - Row 24: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,749 - INFO - Row 25: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,749 - INFO - Row 26: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,752 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:41:16,752 - WARNING - OWNERSHIP_PERCENTAGE batch total ≠ 100 → Actual: 6.00 for rows 27 to 30
2025-07-14 17:41:16,753 - INFO - Row 27: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,753 - INFO - Row 28: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,753 - INFO - Row 29: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,753 - INFO - Row 30: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:41:16,802 - INFO - Excel saved to: data\outputs\Fundholding_Data _2 1_validated.xlsx
2025-07-14 17:41:16,807 - INFO - JSON saved to: data\outputs\Fundholding_Data _2 1_validated.json
2025-07-14 17:46:04,262 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 17:46:04,262 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 17:46:04,263 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 17:46:04,438 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 17:46:04,438 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,438 - INFO - Row 2: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,438 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,438 - INFO - Row 3: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,438 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,438 - INFO - Row 4: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,438 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,438 - INFO - Row 5: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,438 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,438 - INFO - Row 6: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,438 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,438 - INFO - Row 7: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,438 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,438 - INFO - Row 8: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,453 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,454 - INFO - Row 9: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,454 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,454 - INFO - Row 10: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,455 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,455 - INFO - Row 11: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,456 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,456 - INFO - Row 12: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,457 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,457 - INFO - Row 13: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,458 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,458 - INFO - Row 14: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,458 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,459 - INFO - Row 15: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,459 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,459 - INFO - Row 16: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,459 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,460 - INFO - Row 17: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,460 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,462 - INFO - Row 18: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,463 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,463 - INFO - Row 19: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,464 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,464 - INFO - Row 20: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,465 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,466 - INFO - Row 21: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,466 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,467 - INFO - Row 22: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,467 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,469 - INFO - Row 23: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,470 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,470 - INFO - Row 24: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,471 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,472 - INFO - Row 25: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,472 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,472 - INFO - Row 26: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,472 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,474 - INFO - Row 27: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,474 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,474 - INFO - Row 28: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,474 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,474 - INFO - Row 29: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,476 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,476 - INFO - Row 30: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,476 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,476 - INFO - Row 31: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,476 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,476 - INFO - Row 32: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,478 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,478 - INFO - Row 33: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,478 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,478 - INFO - Row 34: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,479 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,479 - INFO - Row 35: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,479 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,480 - INFO - Row 36: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,481 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,481 - INFO - Row 37: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,482 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,482 - INFO - Row 38: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,482 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,483 - INFO - Row 39: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,483 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,483 - INFO - Row 40: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,484 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,484 - INFO - Row 41: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,484 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,484 - INFO - Row 42: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,484 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,484 - INFO - Row 43: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,484 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,486 - INFO - Row 44: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,487 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,487 - INFO - Row 45: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,487 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,487 - INFO - Row 46: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,489 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,489 - INFO - Row 47: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,489 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,489 - INFO - Row 48: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,491 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,491 - INFO - Row 49: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,491 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,491 - INFO - Row 50: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,491 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,491 - INFO - Row 51: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,493 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,497 - INFO - Row 52: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,497 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,499 - INFO - Row 53: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,499 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,501 - INFO - Row 54: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,502 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,502 - INFO - Row 55: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,503 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,504 - INFO - Row 56: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,504 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,504 - INFO - Row 57: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,504 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,506 - INFO - Row 58: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,506 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,507 - INFO - Row 59: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,507 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,508 - INFO - Row 60: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,508 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,508 - INFO - Row 61: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,509 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,509 - INFO - Row 62: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,510 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,510 - INFO - Row 63: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,511 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,511 - INFO - Row 64: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,511 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,511 - INFO - Row 65: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,511 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,511 - INFO - Row 66: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,514 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,514 - INFO - Row 67: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,515 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,515 - INFO - Row 68: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,515 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,515 - INFO - Row 69: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,516 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,516 - INFO - Row 70: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,516 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,517 - INFO - Row 71: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,517 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,518 - INFO - Row 72: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,518 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,518 - INFO - Row 73: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,518 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,518 - INFO - Row 74: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,520 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,520 - INFO - Row 75: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,520 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,520 - INFO - Row 76: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,520 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,520 - INFO - Row 77: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,522 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,522 - INFO - Row 78: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,522 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,524 - INFO - Row 79: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,524 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,524 - INFO - Row 80: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,524 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,526 - INFO - Row 81: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,526 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,526 - INFO - Row 82: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,528 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,528 - INFO - Row 83: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,528 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,528 - INFO - Row 84: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,528 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,530 - INFO - Row 85: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,530 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,530 - INFO - Row 86: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,532 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,532 - INFO - Row 87: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,532 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,533 - INFO - Row 88: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,533 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,533 - INFO - Row 89: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,534 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,534 - INFO - Row 90: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,534 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,534 - INFO - Row 91: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,535 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,535 - INFO - Row 92: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,536 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,537 - INFO - Row 93: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,538 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,539 - INFO - Row 94: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,540 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,541 - INFO - Row 95: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,542 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,542 - INFO - Row 96: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,543 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,545 - INFO - Row 97: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,545 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,546 - INFO - Row 98: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,547 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,547 - INFO - Row 99: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,547 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,547 - INFO - Row 100: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,549 - ERROR - Batch validation error: '"is_correct"'
2025-07-14 17:46:04,549 - INFO - Row 101: FAIL - Batch LLM Error: '"is_correct"'
2025-07-14 17:46:04,549 - WARNING - OWNERSHIP_PERCENTAGE column sum ≠ 100 → Actual: 4264.59
2025-07-14 17:46:04,620 - INFO - Excel saved to: data\outputs\FundHoldings_WithErrors_validated.xlsx
2025-07-14 17:46:04,624 - INFO - JSON saved to: data\outputs\FundHoldings_WithErrors_validated.json
2025-07-14 17:47:48,350 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 17:47:48,352 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 17:47:48,352 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 17:47:48,547 - ERROR - OWNERSHIP_PERCENTAGE sum ≠ 100 → Actual: 4264.59
2025-07-14 17:47:48,547 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 17:47:55,959 - INFO - Row 2: OK - All validation rules are satisfied for Row #2.
2025-07-14 17:47:55,959 - INFO - Row 3: OK - All validation rules are satisfied for Row #3.
2025-07-14 17:47:55,959 - INFO - Row 4: FAIL - OWNERSHIP_PERCENTAGE is null and should be between 0 and 100 for Row #4.
2025-07-14 17:47:55,960 - INFO - Row 5: OK - All validation rules are satisfied for Row #5.
2025-07-14 17:47:55,960 - INFO - Row 6: FAIL - OWNERSHIP_PERCENTAGE sum across rows does not equal 100.
2025-07-14 17:47:59,392 - INFO - Row 7: OK - Row #7 data passes all validation checks.
2025-07-14 17:47:59,392 - INFO - Row 8: OK - Row #8 data passes all validation checks.
2025-07-14 17:47:59,392 - INFO - Row 9: OK - Row #9 data passes all validation checks.
2025-07-14 17:47:59,392 - INFO - Row 10: OK - Row #10 data passes all validation checks.
2025-07-14 17:47:59,392 - INFO - Row 11: FAIL - Row #11 UNIQUE_ID is null or empty.
2025-07-14 17:48:03,586 - INFO - Row 12: FAIL - UNIQUE_ID is null or empty.
2025-07-14 17:48:03,586 - INFO - Row 13: OK - The row data passes all validation checks.
2025-07-14 17:48:03,586 - INFO - Row 14: OK - The row data passes all validation checks.
2025-07-14 17:48:03,586 - INFO - Row 15: OK - The row data passes all validation checks.
2025-07-14 17:48:03,587 - INFO - Row 16: OK - The row data passes all validation checks.
2025-07-14 17:48:06,662 - INFO - Row 17: OK - Row data is valid
2025-07-14 17:48:06,662 - INFO - Row 18: FAIL - OWNERSHIP_PERCENTAGE is null
2025-07-14 17:48:06,662 - INFO - Row 19: OK - Row data is valid
2025-07-14 17:48:06,662 - INFO - Row 20: OK - Row data is valid
2025-07-14 17:48:06,662 - INFO - Row 21: OK - Row data is valid
2025-07-14 17:48:10,592 - INFO - Row 22: OK - Row #22 passes all validation checks.
2025-07-14 17:48:10,592 - INFO - Row 23: OK - Row #23 passes all validation checks.
2025-07-14 17:48:10,592 - INFO - Row 24: OK - Row #24 passes all validation checks.
2025-07-14 17:48:10,592 - INFO - Row 25: OK - Row #25 passes all validation checks.
2025-07-14 17:48:10,592 - INFO - Row 26: FAIL - Row #26 fails null_check for all fields.
2025-07-14 17:48:14,494 - INFO - Row 27: OK - Row data is valid according to the validation rules.
2025-07-14 17:48:14,494 - INFO - Row 28: OK - Row data is valid according to the validation rules.
2025-07-14 17:48:14,494 - INFO - Row 29: OK - Row data is valid according to the validation rules.
2025-07-14 17:48:14,494 - INFO - Row 30: OK - Row data is valid according to the validation rules.
2025-07-14 17:48:14,494 - INFO - Row 31: FAIL - The sum of OWNERSHIP_PERCENTAGE across all rows does not equal 100.
2025-07-14 17:48:17,471 - INFO - Row 32: OK - Row data is valid
2025-07-14 17:48:17,476 - INFO - Row 33: OK - Row data is valid
2025-07-14 17:48:17,476 - INFO - Row 34: FAIL - OWNERSHIP_PERCENTAGE is null
2025-07-14 17:48:17,477 - INFO - Row 35: OK - Row data is valid
2025-07-14 17:48:17,477 - INFO - Row 36: OK - Row data is valid
2025-07-14 17:48:20,980 - INFO - Row 37: OK - 
2025-07-14 17:48:20,980 - INFO - Row 38: OK - 
2025-07-14 17:48:20,980 - INFO - Row 39: OK - 
2025-07-14 17:48:20,981 - INFO - Row 40: OK - 
2025-07-14 17:48:20,981 - INFO - Row 41: FAIL - The sum of OWNERSHIP_PERCENTAGE across rows does not equal 100.
2025-07-14 17:48:26,254 - INFO - Row 42: OK - All data is valid according to the rules provided.
2025-07-14 17:48:26,255 - INFO - Row 43: FAIL - NAV is null which violates the null_check rule.
2025-07-14 17:48:26,256 - INFO - Row 44: FAIL - NAV is null which violates the null_check rule.
2025-07-14 17:48:26,256 - INFO - Row 45: FAIL - UNIQUE_ID, PORTFOLIO_ID, REGISTERED_HOLDER, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL, PERIOD, and FUND_NAME are null which violates the null_check rule.
2025-07-14 17:48:26,257 - INFO - Row 46: OK - All data is valid according to the rules provided.
2025-07-14 17:48:30,375 - INFO - Row 47: OK - Row data is valid
2025-07-14 17:48:30,375 - INFO - Row 48: OK - Row data is valid
2025-07-14 17:48:30,376 - INFO - Row 49: FAIL - OWNERSHIP_PERCENTAGE is null
2025-07-14 17:48:30,376 - INFO - Row 50: OK - Row data is valid
2025-07-14 17:48:30,377 - INFO - Row 51: OK - Row data is valid
2025-07-14 17:48:34,226 - INFO - Row 52: FAIL - NAV should not be null or zero.
2025-07-14 17:48:34,227 - INFO - Row 53: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero.
2025-07-14 17:48:34,227 - INFO - Row 54: FAIL - UNIQUE_ID should not be null or empty.
2025-07-14 17:48:34,228 - INFO - Row 55: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100.
2025-07-14 17:48:34,228 - INFO - Row 56: OK - All checks are passed.
2025-07-14 17:48:37,105 - INFO - Row 57: OK - 
2025-07-14 17:48:37,105 - INFO - Row 58: OK - 
2025-07-14 17:48:37,106 - INFO - Row 59: OK - 
2025-07-14 17:48:37,106 - INFO - Row 60: OK - 
2025-07-14 17:48:37,107 - INFO - Row 61: FAIL - The sum of OWNERSHIP_PERCENTAGE across rows does not equal 100.
2025-07-14 17:48:40,679 - INFO - Row 62: OK - Row data meets all validation criteria
2025-07-14 17:48:40,679 - INFO - Row 63: OK - Row data meets all validation criteria
2025-07-14 17:48:40,680 - INFO - Row 64: OK - Row data meets all validation criteria
2025-07-14 17:48:40,680 - INFO - Row 65: FAIL - OWNERSHIP_PERCENTAGE exceeds 100% when summed with other rows
2025-07-14 17:48:40,681 - INFO - Row 66: OK - Row data meets all validation criteria
2025-07-14 17:48:44,740 - INFO - Row 67: OK - All validation rules are satisfied for Row #67.
2025-07-14 17:48:44,741 - INFO - Row 68: OK - All validation rules are satisfied for Row #68.
2025-07-14 17:48:44,741 - INFO - Row 69: FAIL - The sum of OWNERSHIP_PERCENTAGE across all rows does not equal 100.
2025-07-14 17:48:44,742 - INFO - Row 70: OK - All validation rules are satisfied for Row #70.
2025-07-14 17:48:44,742 - INFO - Row 71: OK - All validation rules are satisfied for Row #71.
2025-07-14 17:48:48,074 - INFO - Row 72: OK - Row data meets all validation criteria
2025-07-14 17:48:48,075 - INFO - Row 73: OK - Row data meets all validation criteria
2025-07-14 17:48:48,075 - INFO - Row 74: OK - Row data meets all validation criteria
2025-07-14 17:48:48,075 - INFO - Row 75: FAIL - UNIQUE_ID is null or empty
2025-07-14 17:48:48,075 - INFO - Row 76: OK - Row data meets all validation criteria
2025-07-14 17:48:51,398 - INFO - Row 77: OK - 
2025-07-14 17:48:51,398 - INFO - Row 78: OK - 
2025-07-14 17:48:51,398 - INFO - Row 79: OK - 
2025-07-14 17:48:51,398 - INFO - Row 80: OK - 
2025-07-14 17:48:51,398 - INFO - Row 81: FAIL - The sum of OWNERSHIP_PERCENTAGE does not equal 100.
2025-07-14 17:48:55,178 - INFO - Row 82: OK - All validation rules are satisfied for Row #82.
2025-07-14 17:48:55,179 - INFO - Row 83: FAIL - NAV is null for Row #83.
2025-07-14 17:48:55,179 - INFO - Row 84: OK - All validation rules are satisfied for Row #84.
2025-07-14 17:48:55,179 - INFO - Row 85: OK - All validation rules are satisfied for Row #85.
2025-07-14 17:48:55,181 - INFO - Row 86: OK - All validation rules are satisfied for Row #86.
2025-07-14 17:48:59,198 - INFO - Row 87: OK - The data row is valid according to the specified rules.
2025-07-14 17:48:59,199 - INFO - Row 88: OK - The data row is valid according to the specified rules.
2025-07-14 17:48:59,199 - INFO - Row 89: OK - The data row is valid according to the specified rules.
2025-07-14 17:48:59,200 - INFO - Row 90: FAIL - UNIQUE_ID is null or empty.
2025-07-14 17:48:59,200 - INFO - Row 91: OK - The data row is valid according to the specified rules.
2025-07-14 17:49:02,395 - INFO - Row 92: FAIL - NAV should not be null or zero.
2025-07-14 17:49:02,395 - INFO - Row 93: OK - All checks passed.
2025-07-14 17:49:02,396 - INFO - Row 94: OK - All checks passed.
2025-07-14 17:49:02,396 - INFO - Row 95: OK - All checks passed.
2025-07-14 17:49:02,396 - INFO - Row 96: OK - All checks passed.
2025-07-14 17:49:06,827 - INFO - Row 97: OK - Row data meets all validation criteria
2025-07-14 17:49:06,827 - INFO - Row 98: FAIL - UNIQUE_ID is null or empty; NAV is null; OWNERSHIP_PERCENTAGE is null; CAPITAL_CALLED is null; NO_OF_SHARES is null; COMMITTED_CAPITAL is null
2025-07-14 17:49:06,828 - INFO - Row 99: OK - Row data meets all validation criteria
2025-07-14 17:49:06,828 - INFO - Row 100: OK - Row data meets all validation criteria
2025-07-14 17:49:06,828 - INFO - Row 101: OK - Row data meets all validation criteria
2025-07-14 17:49:06,917 - INFO - Validation completed. Output saved to: data\outputs\FundHoldings_WithErrors_validated.xlsx
2025-07-14 17:55:12,438 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 17:55:12,439 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 17:55:12,439 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 17:55:12,614 - INFO - Number of rows to validate: 100
2025-07-14 17:55:12,614 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 17:55:14,143 - INFO - Row 2: OK - All validation rules have been passed for the given row data.
2025-07-14 17:55:16,904 - INFO - Row 3: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, but the sum of OWNERSHIP_PERCENTAGE across all rows is not equal to 100 as per the OWNERSHIP_PERCENTAGE rule
2025-07-14 17:55:18,113 - INFO - Row 4: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero
2025-07-14 17:55:20,678 - INFO - Row 5: FAIL - OWNERSHIP_PERCENTAGE must sum up to 100 across all rows, but only a single row is provided. Cannot validate OWNERSHIP_PERCENTAGE without additional data.
2025-07-14 17:55:21,919 - INFO - Row 6: FAIL - OWNERSHIP_PERCENTAGE is not equal to 100.
2025-07-14 17:55:23,806 - INFO - Row 7: OK - All validation rules have been applied and the data row is correct.
2025-07-14 17:55:25,415 - INFO - Row 8: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100
2025-07-14 17:55:27,354 - INFO - Row 9: FAIL - OWNERSHIP_PERCENTAGE must sum up to 100 across all rows; this cannot be validated with a single row.
2025-07-14 17:55:29,014 - INFO - Row 10: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100.
2025-07-14 17:55:30,596 - INFO - Row 11: FAIL - UNIQUE_ID is null or empty which violates the null_check rule
2025-07-14 17:55:32,089 - INFO - Row 12: FAIL - UNIQUE_ID is null or empty; OWNERSHIP_PERCENTAGE is not between 0 and 100
2025-07-14 17:55:33,434 - INFO - Row 13: OK - All validation rules have been passed for the row data.
2025-07-14 17:55:35,401 - INFO - Row 14: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100. The sum of OWNERSHIP_PERCENTAGE across all rows should equal 100, but this cannot be validated with a single row.
2025-07-14 17:55:36,818 - INFO - Row 15: FAIL - OWNERSHIP_PERCENTAGE value is not between 0 and 100
2025-07-14 17:55:38,877 - INFO - Row 16: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, and UNIQUE_ID should not start with 'TOTAL' or contain non-alphanumeric characters
2025-07-14 17:55:40,763 - INFO - Row 17: OK - All validation rules have been successfully applied and the data is correct.
2025-07-14 17:55:41,975 - INFO - Row 18: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero
2025-07-14 17:55:44,460 - INFO - Row 19: FAIL - OWNERSHIP_PERCENTAGE must sum up to 100 across all rows, which cannot be determined from a single row. Additionally, the UNIQUE_ID format check cannot be fully validated without access to the complete dataset to check for duplicates.
2025-07-14 17:55:46,162 - INFO - Row 20: FAIL - OWNERSHIP_PERCENTAGE is not equal to 100
2025-07-14 17:55:48,023 - INFO - Row 21: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, and the sum of OWNERSHIP_PERCENTAGE across all rows should equal 100.
2025-07-14 17:55:51,632 - INFO - Row 22: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, UNIQUE_ID should not be null or empty, UNIQUE_ID should not have duplicates, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero
2025-07-14 17:55:53,842 - INFO - Row 23: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, and OWNERSHIP_PERCENTAGE sum must equal 100
2025-07-14 17:55:55,314 - INFO - Row 24: OK - All validation rules passed
2025-07-14 17:55:57,351 - INFO - Row 25: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, and NAV, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero.
2025-07-14 17:55:59,536 - INFO - Row 26: FAIL - UNIQUE_ID is null or empty, NAV is null or zero, OWNERSHIP_PERCENTAGE is null or zero, CAPITAL_CALLED is null or zero, NO_OF_SHARES is null or zero, COMMITTED_CAPITAL is null or zero
2025-07-14 17:56:01,952 - INFO - Row 27: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', and the sum of OWNERSHIP_PERCENTAGE across all rows should equal 100
2025-07-14 17:56:04,519 - INFO - Row 28: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, OWNERSHIP_PERCENTAGE sum should be equal to 100
2025-07-14 17:56:05,996 - INFO - Row 29: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100
2025-07-14 17:56:07,508 - INFO - Row 30: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100
2025-07-14 17:56:10,302 - INFO - Row 31: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100. UNIQUE_ID contains invalid format starting with 'TOTAL' or contains non-alphanumeric characters.
2025-07-14 17:56:11,605 - INFO - Row 32: FAIL - OWNERSHIP_PERCENTAGE is not equal to 100
2025-07-14 17:56:14,105 - INFO - Row 33: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, and OWNERSHIP_PERCENTAGE sum is not equal to 100.
2025-07-14 17:56:15,554 - INFO - Row 34: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero
2025-07-14 17:56:16,712 - INFO - Row 35: FAIL - OWNERSHIP_PERCENTAGE is not equal to 100
2025-07-14 17:56:17,743 - INFO - Row 36: FAIL - OWNERSHIP_PERCENTAGE sum is not equal to 100
2025-07-14 17:56:19,502 - INFO - Row 37: OK - All validation rules have been met for the row data.
2025-07-14 17:56:21,341 - INFO - Row 38: OK - All validation rules have been passed for the given row data.
2025-07-14 17:56:22,496 - INFO - Row 39: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100
2025-07-14 17:56:25,349 - INFO - Row 40: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero
2025-07-14 17:56:27,635 - INFO - Row 41: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, and the sum of OWNERSHIP_PERCENTAGE across all rows should equal 100
2025-07-14 17:56:30,606 - INFO - Row 42: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, and the sum of OWNERSHIP_PERCENTAGE for all rows should equal 100. Since only one row is provided and the OWNERSHIP_PERCENTAGE is not 100, the data cannot be validated as correct without additional context.
2025-07-14 17:56:31,939 - INFO - Row 43: FAIL - NAV should not be null or zero
2025-07-14 17:56:33,343 - INFO - Row 44: FAIL - NAV is null which violates the null_check rule
2025-07-14 17:56:36,210 - INFO - Row 45: FAIL - UNIQUE_ID is null or empty; NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero; File should not be empty; UNIQUE_ID should not be null or empty; OWNERSHIP_PERCENTAGE sum check cannot be performed as it is null or zero.
2025-07-14 17:56:37,516 - INFO - Row 46: OK - All validation rules have been applied and the data row is correct.
2025-07-14 17:56:40,008 - INFO - Row 47: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', and the sum of OWNERSHIP_PERCENTAGE across all rows should equal 100
2025-07-14 17:56:43,753 - INFO - Row 48: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, and OWNERSHIP_PERCENTAGE sum across all rows should equal 100
2025-07-14 17:56:45,375 - INFO - Row 49: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero
2025-07-14 17:56:46,862 - INFO - Row 50: FAIL - OWNERSHIP_PERCENTAGE sum is not equal to 100
2025-07-14 17:56:50,296 - INFO - Row 51: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, and the sum of OWNERSHIP_PERCENTAGE across all rows should equal 100. The provided OWNERSHIP_PERCENTAGE is within range, but without other rows, we cannot validate if the sum equals 100. Additionally, UNIQUE_ID starts with 'UID_' which is acceptable, but without the complete dataset, we cannot determine if it is unique.
2025-07-14 17:56:51,601 - INFO - Row 52: FAIL - NAV should not be null or zero
2025-07-14 17:56:53,361 - INFO - Row 53: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero; OWNERSHIP_PERCENTAGE is not a valid number
2025-07-14 17:56:56,011 - INFO - Row 54: FAIL - UNIQUE_ID is null or empty which violates the null_check rule
2025-07-14 17:56:57,423 - INFO - Row 55: FAIL - OWNERSHIP_PERCENTAGE must sum up to 100 across all rows, which cannot be determined from a single row.
2025-07-14 17:56:59,708 - INFO - Row 56: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100. The sum of OWNERSHIP_PERCENTAGE across all rows should equal 100, but this cannot be validated with a single row.
2025-07-14 17:57:02,782 - INFO - Row 57: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, UNIQUE_ID should not be null or empty, UNIQUE_ID should not have duplicates, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero
2025-07-14 17:57:06,280 - INFO - Row 58: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero, UNIQUE_ID should not be null or empty, UNIQUE_ID should not have duplicates
2025-07-14 17:57:08,429 - INFO - Row 59: FAIL - OWNERSHIP_PERCENTAGE should sum up to 100 across all rows, but it is not verifiable with a single row provided
2025-07-14 17:57:11,432 - INFO - Row 60: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, UNIQUE_ID should not be null or empty, UNIQUE_ID should not have duplicates, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero
2025-07-14 17:57:13,878 - INFO - Row 61: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, and the sum of OWNERSHIP_PERCENTAGE across all rows should equal 100.
2025-07-14 17:57:16,710 - INFO - Row 62: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100
2025-07-14 17:57:19,259 - INFO - Row 63: FAIL - OWNERSHIP_PERCENTAGE should sum up to 100 across all rows, but since only one row is provided, it cannot be validated if the sum is 100. Additionally, UNIQUE_ID starts with 'UID_' which is not alphanumeric, violating the format check rule.
2025-07-14 17:57:21,945 - INFO - Row 64: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, and OWNERSHIP_PERCENTAGE sum across all rows should equal 100
2025-07-14 17:57:24,414 - INFO - Row 65: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', and the sum of OWNERSHIP_PERCENTAGE across all rows should equal 100.
2025-07-14 17:57:26,363 - INFO - Row 66: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100. The sum of OWNERSHIP_PERCENTAGE across all rows should equal 100, but this cannot be validated with a single row.
2025-07-14 17:57:29,739 - INFO - Row 67: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero, UNIQUE_ID should not be null or empty, UNIQUE_ID should not have duplicates
2025-07-14 17:57:32,456 - INFO - Row 68: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, and UNIQUE_ID should not start with 'TOTAL', be null or empty, and should not have duplicates. Additionally, the sum of OWNERSHIP_PERCENTAGE across all rows should equal 100 for the data to be valid.
2025-07-14 17:57:33,946 - INFO - Row 69: FAIL - OWNERSHIP_PERCENTAGE is not equal to 100
2025-07-14 17:57:36,194 - INFO - Row 70: FAIL - OWNERSHIP_PERCENTAGE should sum up to 100 across all rows, but as a single row cannot be validated for sum, this row's data is considered incomplete without the context of other rows.
2025-07-14 17:57:38,093 - INFO - Row 71: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', and the sum of OWNERSHIP_PERCENTAGE across all rows should equal 100.
2025-07-14 17:57:40,143 - INFO - Row 72: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, but the sum of OWNERSHIP_PERCENTAGE across all rows is not equal to 100 as per the provided rule
2025-07-14 17:57:41,557 - INFO - Row 73: OK - All validation rules have been passed for the given row data.
2025-07-14 17:57:45,215 - INFO - Row 74: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL' (case insensitive), UNIQUE_ID should only contain alphanumeric characters, NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero, UNIQUE_ID should not be null or empty, UNIQUE_ID should not have duplicates
2025-07-14 17:57:46,804 - INFO - Row 75: FAIL - UNIQUE_ID is null or empty which violates the null_check rule
2025-07-14 17:57:49,902 - INFO - Row 76: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, and UNIQUE_ID should not start with 'TOTAL', contain only alphanumeric characters, and not be null or empty. NAV, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be null or zero. The file should not be empty, and UNIQUE_ID should not have duplicates. The OWNERSHIP_PERCENTAGE sum is not equal to 100.
2025-07-14 17:57:52,218 - INFO - Row 77: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, but the sum of OWNERSHIP_PERCENTAGE across all rows is not equal to 100 as per the OWNERSHIP_PERCENTAGE rule.
2025-07-14 17:57:54,624 - INFO - Row 78: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, UNIQUE_ID should not start with 'TOTAL', UNIQUE_ID should only contain alphanumeric characters, and the sum of OWNERSHIP_PERCENTAGE for all rows should equal 100.
2025-07-14 17:57:56,059 - INFO - Row 79: FAIL - OWNERSHIP_PERCENTAGE is not summing up to 100 as per the provided data.
2025-07-14 17:57:57,588 - INFO - Row 80: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100
2025-07-14 17:58:00,871 - INFO - Row 81: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100. The sum of OWNERSHIP_PERCENTAGE across all rows should equal 100, but this cannot be validated with a single row.
2025-07-14 17:58:03,740 - INFO - Row 82: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, and the sum of OWNERSHIP_PERCENTAGE for all rows should equal 100. The provided OWNERSHIP_PERCENTAGE is 53.95, but without other rows, we cannot validate if the sum equals 100.
2025-07-14 17:58:05,231 - INFO - Row 83: FAIL - NAV should not be null or zero
2025-07-14 18:00:28,728 - INFO - Loaded prompt template from: ContextGuardrail/data_validation_prompts.txt
2025-07-14 18:00:28,729 - INFO - Loaded validation rules from: data/outputs/data_validation.json
2025-07-14 18:00:28,729 - INFO - Input file path: data\inputs\FundHoldings_WithErrors.xlsx
2025-07-14 18:00:28,925 - INFO - Number of rows to validate: 100
2025-07-14 18:00:28,926 - INFO - Started validation for file: data\inputs\FundHoldings_WithErrors.xlsx with 100 rows.
2025-07-14 18:00:30,544 - INFO - Row 2: OK - All validation rules passed
2025-07-14 18:00:33,640 - INFO - Row 3: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100, but the sum of OWNERSHIP_PERCENTAGE across all rows is not equal to 100 as per the business rule.
2025-07-14 18:00:35,490 - INFO - Row 4: FAIL - OWNERSHIP_PERCENTAGE should not be null or zero
2025-07-14 18:00:38,057 - INFO - Row 5: FAIL - OWNERSHIP_PERCENTAGE should be between 0 and 100. The sum of OWNERSHIP_PERCENTAGE across all rows should equal 100, but this cannot be validated with a single row.
2025-07-14 18:00:39,756 - INFO - Row 6: FAIL - OWNERSHIP_PERCENTAGE is not equal to 100
2025-07-14 18:00:41,267 - INFO - Row 7: OK - All validation rules have been met for the given row data.
2025-07-14 18:53:40.767592 - ❌ OWNERSHIP_PERCENTAGE sum != 100. Found: 4264.59
2025-07-14 18:58:48.358150 - ❌ OWNERSHIP_PERCENTAGE sum != 100. Found: 4264.59
2025-07-14 19:22:19,065 - OWNERSHIP_PERCENTAGE total is not 100. Found: 4264.59
